package cn.jbolt.admin.llmmodel;

import com.jfinal.aop.Inject;
import cn.jbolt.core.controller.base.JBoltBaseController;
import cn.jbolt.core.permission.CheckPermission;
import cn.jbolt._admin.permission.PermissionKey;
import cn.jbolt.core.permission.UnCheckIfSystemAdmin;
import com.jfinal.core.Path;
import com.jfinal.aop.Before;
import cn.jbolt.core.permission.JBoltAdminAuthInterceptor;
import com.jfinal.core.paragetter.Para;
import com.jfinal.plugin.activerecord.tx.Tx;
import cn.jbolt.core.base.JBoltMsg;
import cn.jbolt.common.model.LlmModel;
import cn.jbolt.common.model.LlmProvider;
import cn.jbolt.admin.llmprovider.LlmProviderService;
import cn.jbolt.core.permission.UnCheck;

/**
 * 大模型具体模型表
 * @ClassName: LlmModelAdminController
 * @author: 总管理
 * @date: 2025-05-08 16:52
 */
@CheckPermission(PermissionKey.ADMIN_LLMPROVIDER)
@UnCheckIfSystemAdmin
@Before(JBoltAdminAuthInterceptor.class)
@Path(value = "/admin/llmModel", viewPath = "/_view/admin/llmmodel")
public class LlmModelAdminController extends JBoltBaseController {

	@Inject
	private LlmModelService service;

	@Inject
	private LlmProviderService providerService;

   /**
	* 数据源
	*/
	public void datas() {
		renderJsonData(service.getAdminDatas(getPageNumber(), getPageSize(), getKeywords(), getSortColumn("id"), getSortType("desc"), getLong("providerId"), getBoolean("status")));
	}

   /**
	* 根据提供商ID获取模型列表
	*/
	@UnCheck
	public void listByProvider() {
		Long providerId = getLong("providerId");
		if (providerId == null) {
			renderJsonFail("提供商ID不能为空");
			return;
		}
		renderJsonData(service.getModelsByProviderId(providerId));
	}

   /**
	* 新增
	*/
	public void add() {
		Long providerId = getLong(0);
		if (providerId == null) {
			renderFail("提供商ID不能为空");
			return;
		}

		LlmProvider provider = providerService.findById(providerId);
		if (provider == null) {
			renderFail("提供商不存在");
			return;
		}

		set("provider", provider);

		render("add.html");
	}

   /**
	* 保存
	*/
	@Before(Tx.class)
	public void save(@Para("llmModel")LlmModel llmModel) {
		renderJson(service.save(llmModel));
	}

   /**
	* 编辑
	*/
	public void edit() {
		LlmModel llmModel = service.findById(getLong(0));
		if(llmModel == null){
			renderFail(JBoltMsg.DATA_NOT_EXIST);
			return;
		}

		LlmProvider provider = providerService.findById(llmModel.getProviderId());
		if (provider == null) {
			renderFail("提供商不存在");
			return;
		}

		set("llmModel", llmModel);
		set("provider", provider);

		render("edit.html");
	}

   /**
	* 更新
	*/
	@Before(Tx.class)
	public void update(@Para("llmModel")LlmModel llmModel) {
		renderJson(service.update(llmModel));
	}

   /**
	* 删除
	*/
	@Before(Tx.class)
	public void delete() {
		renderJson(service.deleteById(getLong(0)));
	}

   /**
	* 切换status
	*/
	@Before(Tx.class)
	public void toggleStatus() {
	    renderJson(service.toggleBoolean(getLong(0), "status"));
	}

   /**
	* 根据提供商ID获取模型列表
	*/
	@UnCheck
	public void modelsByProvider() {
		Long providerId = getLong("providerId");
		renderJsonData(service.getModelsByProviderId(providerId));
	}
}
