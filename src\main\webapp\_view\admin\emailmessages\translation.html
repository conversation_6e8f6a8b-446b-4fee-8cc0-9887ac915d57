#@jboltLayout()
#define main()
<div class="jbolt_page">
    <div class="jbolt_page_content">
        <div class="card">
            <div class="card-body">
                <div class="translation-content">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>翻译结果</h3>
                        <div class="d-flex align-items-center">
                            <!-- 图片翻译开关 -->
                            <div class="form-check mr-3">
                                <input class="form-check-input" type="checkbox" id="translateImagesSwitch" checked
                                       title="是否翻译图片内容（产品图等可关闭此选项）" data-toggle="tooltip">
                                <label class="form-check-label" for="translateImagesSwitch">
                                    <i class="fa fa-image"></i> 翻译图片
                                </label>
                            </div>

                            <!-- <button class="btn btn-primary mr-2" onclick="translateContent()">
                                <i class="fa fa-language"></i> 获取翻译
                            </button> -->
                            <button class="btn btn-outline-primary mr-2" id="refreshTranslationBtn" onclick="refreshTranslation()"
                                    title="强制重新翻译 (快捷键: Ctrl+R 或 F5)" data-toggle="tooltip">
                                <i class="fa fa-refresh" id="refreshIcon"></i>
                                <span id="refreshText">强制重新翻译</span>
                                <div class="spinner-border spinner-border-sm d-none" id="refreshSpinner" role="status">
                                    <span class="sr-only">翻译中...</span>
                                </div>
                            </button>
<!--                            <button class="btn btn-success" onclick="forwardWithTranslation()">-->
<!--                                <i class="fa fa-share"></i> 翻译转发-->
<!--                            </button>-->
                            <button class="btn btn-success" onclick="forwardEmail('#(emailId??)')">
                                <i class="fa fa-share"></i> 转发
                            </button>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <ul class="nav nav-tabs card-header-tabs" id="originalTabs" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link active" id="text-tab" data-toggle="tab" href="#textContent" role="tab" aria-controls="textContent" aria-selected="true">原文</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="screenshot-tab" data-toggle="tab" href="#screenshotTab" role="tab" aria-controls="screenshotTab" aria-selected="false">PDF/EXCEL附件转图片</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="attachments-tab" data-toggle="tab" href="#attachmentsTab" role="tab" aria-controls="attachmentsTab" aria-selected="false">原附件图片</a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="card-body">
                                    <div class="tab-content" id="originalTabsContent">
                                        <div class="tab-pane fade show active" id="textContent" role="tabpanel" aria-labelledby="text-tab">
                                            #(content??)
                                        </div>
                                        <div class="tab-pane fade" id="screenshotTab" role="tabpanel" aria-labelledby="screenshot-tab">
                                            <div id="screenshotContent">
                                                <!-- 截图轮播 -->
                                                <div id="screenshotCarousel" class="carousel slide" data-interval="false">
                                                    <div class="carousel-inner" id="screenshotCarouselInner">
                                                        <!-- 截图将通过JavaScript动态加载 -->
                                                        <div class="text-center py-3" id="screenshotLoading">
                                                            <i class="fa fa-spinner fa-spin"></i> 正在加载截图...
                                                        </div>
                                                    </div>
                                                    <!-- 轮播控制按钮 -->
                                                    <a class="carousel-control-prev" href="#screenshotCarousel" role="button" data-slide="prev">
                                                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                                        <span class="sr-only">上一张</span>
                                                    </a>
                                                    <a class="carousel-control-next" href="#screenshotCarousel" role="button" data-slide="next">
                                                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                                        <span class="sr-only">下一张</span>
                                                    </a>
                                                </div>
                                                
                                                <!-- 截图计数和控制 -->
                                                <div class="d-flex justify-content-between align-items-center mt-3">
                                                    <div id="screenshotCounter" class="text-muted">0/0</div>
                                                    <div class="btn-group">
                                                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleScreenshotSize()">
                                                            <i class="fa fa-expand"></i> 切换大小
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-primary" onclick="showThumbnailGallery()">
                                                            <i class="fa fa-th"></i> 缩略图
                                                        </button>
                                                    </div>
                                                </div>
                                                
                                                <!-- 缩略图画廊 -->
                                                <div id="thumbnailGallery" class="mt-3" style="display: none;">
                                                    <div class="row" id="thumbnailContainer">
                                                        <!-- 缩略图将通过JavaScript动态加载 -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tab-pane fade" id="attachmentsTab" role="tabpanel" aria-labelledby="attachments-tab">
                                            <div id="attachmentsContent">
                                                <!-- 邮件原附件图片展示 -->
                                                <div id="attachmentImagesCarousel" class="carousel slide" data-interval="false">
                                                    <div class="carousel-inner" id="attachmentImagesCarouselInner">
                                                        <!-- 附件图片将通过JavaScript动态加载 -->
                                                        <div class="text-center py-3" id="attachmentImagesLoading">
                                                            <i class="fa fa-spinner fa-spin"></i> 正在加载原附件图片...
                                                        </div>
                                                    </div>
                                                    <!-- 轮播控制按钮 -->
                                                    <a class="carousel-control-prev" href="#attachmentImagesCarousel" role="button" data-slide="prev">
                                                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                                        <span class="sr-only">上一张</span>
                                                    </a>
                                                    <a class="carousel-control-next" href="#attachmentImagesCarousel" role="button" data-slide="next">
                                                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                                        <span class="sr-only">下一张</span>
                                                    </a>
                                                </div>
                                                
                                                <!-- 附件图片计数和控制 -->
                                                <div class="d-flex justify-content-between align-items-center mt-3">
                                                    <div id="attachmentImagesCounter" class="text-muted">0/0</div>
                                                    <div class="btn-group">
                                                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleAttachmentImageSize()">
                                                            <i class="fa fa-expand"></i> 切换大小
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-primary" onclick="showAttachmentThumbnailGallery()">
                                                            <i class="fa fa-th"></i> 缩略图
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-success" onclick="downloadAllAttachmentImages()">
                                                            <i class="fa fa-download"></i> 下载全部
                                                        </button>
                                                    </div>
                                                </div>
                                                
                                                <!-- 附件图片缩略图画廊 -->
                                                <div id="attachmentThumbnailGallery" class="mt-3" style="display: none;">
                                                    <div class="row" id="attachmentThumbnailContainer">
                                                        <!-- 缩略图将通过JavaScript动态加载 -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <ul class="nav nav-tabs card-header-tabs" id="translationTabs" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link active" id="subject-translation-tab" data-toggle="tab" href="#subject-translation" role="tab">标题翻译</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="content-translation-tab" data-toggle="tab" href="#content-translation" role="tab">内容翻译</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="images-translation-tab" data-toggle="tab" href="#images-translation" role="tab">图片翻译</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="combined-translation-tab" data-toggle="tab" href="#combined-translation" role="tab">完整翻译</a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="card-body">
                                    <div class="tab-content" id="translationTabContent">
                                        <!-- 标题翻译 -->
                                        <div class="tab-pane fade show active" id="subject-translation" role="tabpanel">
                                            <div class="row">
                                                <div class="col-12">
                                                    <h6 class="text-muted">原标题</h6>
                                                    <div class="border p-2 mb-3 bg-light" id="originalSubject">
                                                        <div class="text-center text-muted" id="subjectLoadingText">
                                                            <i class="fa fa-spinner fa-spin"></i> 加载中...
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <h6 class="text-muted mb-0">翻译标题</h6>
                                                        <div class="btn-group">
                                                            <button class="btn btn-sm btn-outline-primary" onclick="translateSingle('subject')"
                                                                    title="单独翻译标题" data-toggle="tooltip">
                                                                <i class="fa fa-language"></i> 翻译
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-secondary" onclick="editTranslation('subject')"
                                                                    title="编辑翻译内容" data-toggle="tooltip">
                                                                <i class="fa fa-edit"></i> 编辑
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="border p-2 bg-white" id="translatedSubject">
                                                        <div class="text-center text-muted">
                                                            暂无翻译
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 内容翻译 -->
                                        <div class="tab-pane fade" id="content-translation" role="tabpanel">
                                            <div class="row">
                                                <div class="col-12">
                                                    <h6 class="text-muted">原内容</h6>
                                                    <div class="border p-2 mb-3 bg-light original-content-container" id="originalContentText">
                                                        <div class="text-center text-muted">
                                                            <i class="fa fa-spinner fa-spin"></i> 加载中...
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <h6 class="text-muted mb-0">翻译内容</h6>
                                                        <div class="btn-group">
                                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="translateSingle('content')"
                                                                    title="单独翻译内容" data-toggle="tooltip">
                                                                <i class="fa fa-language"></i> 翻译
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="editTranslation('content')"
                                                                    title="编辑翻译内容" data-toggle="tooltip">
                                                                <i class="fa fa-edit"></i> 编辑
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="border p-2 bg-white content-container" id="translatedContentText">
                                                        <div class="text-center text-muted">
                                                            暂无翻译
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 图片翻译 -->
                                        <div class="tab-pane fade" id="images-translation" role="tabpanel">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="text-muted mb-0">图片翻译结果</h6>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="translateSingle('images')"
                                                            title="单独翻译图片" data-toggle="tooltip">
                                                        <i class="fa fa-image"></i> 翻译图片
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="editTranslation('images')"
                                                            title="编辑图片翻译" data-toggle="tooltip">
                                                        <i class="fa fa-edit"></i> 编辑
                                                    </button>
                                                </div>
                                            </div>
                                            <div id="imageTranslationContainer">
                                                <div class="text-center text-muted">
                                                    暂无图片翻译
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 完整翻译 -->
                                        <div class="tab-pane fade" id="combined-translation" role="tabpanel">
                                            <div id="translatedContent">
                                                <div class="text-center" id="loadingText">
                                                    <i class="fa fa-spinner fa-spin"></i> 翻译中...
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图片预览模态框 -->
<div class="modal fade" id="imagePreviewModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">截图预览</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="previewImage" src="" class="img-fluid" alt="截图大图" />
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
#end

#define css()
<style>
    .translation-content {
        padding: 20px;
    }
    .card-body {
        min-height: 300px;
        max-height: 1000px;
        overflow-y: auto;
    }
    #loadingText {
        padding: 20px;
    }
    .card {
        height: 100%;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    .card-header {
        background-color: #f8f9fa;
        font-weight: bold;
        border-bottom: 1px solid rgba(0,0,0,0.125);
    }
    #originalContent, #translatedContent {
        font-size: 14px;
        line-height: 1.6;
        white-space: pre-wrap;
        word-wrap: break-word;
    }

    /* 新增的翻译界面样式 */
    #originalContentText, #translatedContentText {
        max-height: 300px;
        overflow-y: auto;
        font-size: 14px;
        line-height: 1.6;
        white-space: pre-wrap;
        word-wrap: break-word;
    }

    #originalContentText {
        max-height: 200px;
    }

    .image-translation-item {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
        overflow: hidden;
    }

    .image-translation-header {
        background-color: #f8f9fa;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #dee2e6;
        font-weight: bold;
    }

    .image-translation-content {
        padding: 1rem;
    }

    .image-preview {
        max-width: 100%;
        max-height: 200px;
        object-fit: contain;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
    }

    .translation-tabs .nav-link {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }

    .translation-tabs .nav-link.active {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }
    
    /* 截图相关样式 */
    .screenshot-preview {
        max-width: 100%;
        height: auto;
        border: 1px solid #eee;
        border-radius: 4px;
        cursor: pointer;
        transition: transform 0.2s ease;
    }
    
    .screenshot-preview:hover {
        transform: scale(1.02);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .screenshot-large {
        max-height: 600px;
    }
    
    .screenshot-small {
        max-height: 300px;
    }
    
    #screenshotContent, #attachmentsContent {
        padding: 10px;
        background-color: #f9f9f9;
    }
    
    /* 截图文件名样式 */
    .screenshot-filename {
        background-color: rgba(255, 255, 255, 0.9);
        padding: 5px 10px;
        border-radius: 4px;
        margin-bottom: 10px;
        word-break: break-all;
        max-width: 100%;
    }
    
    .screenshot-container {
        padding: 15px;
        background-color: #fff;
        border-radius: 8px;
        margin: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    /* 轮播样式优化 */
    .carousel-item {
        text-align: center;
        background-color: #f9f9f9;
        padding: 10px;
        border-radius: 4px;
    }
    
    .carousel-control-prev,
    .carousel-control-next {
        width: 5%;
        background-color: rgba(0,0,0,0.2);
        border-radius: 0 4px 4px 0;
    }
    
    .carousel-control-prev {
        border-radius: 4px 0 0 4px;
    }
    
    /* 缩略图样式 */
    .thumbnail, .attachment-thumbnail {
        width: 80px;
        height: 60px;
        object-fit: cover;
        border: 2px solid #eee;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
        margin: 5px;
    }
    
    .thumbnail:hover, .attachment-thumbnail:hover {
        border-color: #007bff;
        transform: scale(1.05);
    }
    
    .thumbnail.active, .attachment-thumbnail.active {
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0,123,255,0.5);
    }
    
    /* 缩略图文件名样式 */
    .thumbnail-filename {
        font-size: 10px;
        margin-top: 3px;
        max-width: 80px;
        word-break: break-all;
        line-height: 1.2;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    /* 模态框样式优化 */
    .modal-xl {
        max-width: 90%;
    }
    
    .modal-body {
        padding: 20px;
        background-color: #f9f9f9;
    }
    
    #previewImage {
        max-height: 80vh;
    }
    
    /* Tab样式优化 */
    .nav-tabs .nav-link {
        color: #495057;
        background-color: transparent;
        border-color: transparent;
    }
    
    .nav-tabs .nav-link.active {
        color: #007bff;
        background-color: #fff;
        border-color: #dee2e6 #dee2e6 #fff;
        font-weight: bold;
    }
    
    .card-header-tabs {
        margin-right: 0;
        margin-left: 0;
        margin-bottom: -1px;
    }
    
    /* 进度条和按钮状态样式 */
    #refreshTranslationBtn:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

    #refreshTranslationBtn .spinner-border-sm {
        width: 1rem;
        height: 1rem;
        margin-left: 0.5rem;
    }

    .translation-progress {
        background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 0.75rem 1rem;
        border-radius: 0.375rem;
        margin: 0.5rem 0;
        box-shadow: 0 2px 4px rgba(0,123,255,0.3);
    }

    .translation-progress .fa-spinner {
        margin-right: 0.5rem;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 进度条动画 */
    .progress-bar-animated {
        animation: progress-bar-stripes 1s linear infinite;
    }

    @keyframes progress-bar-stripes {
        0% { background-position: 1rem 0; }
        100% { background-position: 0 0; }
    }

    /* 翻译状态指示器 */
    .translation-status {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .translation-status.translating {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .translation-status.completed {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .translation-status.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    /* 图片翻译开关样式 */
    .form-check {
        display: flex;
        align-items: center;
        margin-bottom: 0;
    }

    .form-check-input {
        margin-right: 0.5rem;
        margin-top: 0;
    }

    .form-check-label {
        margin-bottom: 0;
        font-size: 0.9rem;
        color: #495057;
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .form-check-label i {
        margin-right: 0.25rem;
        color: #6c757d;
    }

    .form-check-input:checked + .form-check-label {
        color: #007bff;
    }

    .form-check-input:checked + .form-check-label i {
        color: #007bff;
    }

    .form-check-input:disabled + .form-check-label {
        color: #6c757d;
        cursor: not-allowed;
    }

    /* 内容容器样式 */
    .content-container {
        max-height: 300px;
        overflow-y: auto;
    }

    .original-content-container {
        max-height: 200px;
        overflow-y: auto;
    }

    /* 单独翻译按钮样式 */
    .btn-group .btn-sm {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .btn-group .btn-sm i {
        margin-right: 0.25rem;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .row {
            flex-direction: column;
        }
        .col-md-6 {
            margin-bottom: 20px;
        }

        #refreshTranslationBtn {
            font-size: 0.875rem;
            padding: 0.5rem 0.75rem;
        }

        .d-flex.align-items-center {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .form-check {
            margin-bottom: 0.5rem;
            width: 100%;
        }

        .form-check-label {
            font-size: 0.8rem;
        }
    }
</style>
#end

#define js()
<script>
// 全局变量，用于存储所有截图
let allScreenshots = [];
let currentScreenshotIndex = 0;

// 全局变量，用于存储所有邮件原附件图片
let allAttachmentImages = [];
let currentAttachmentImageIndex = 0;

// 全局变量，用于跟踪翻译状态
let isTranslating = false;

// 初始化图片翻译开关
function initTranslateImagesSwitch() {
    const $switch = $('#translateImagesSwitch');

    // 从本地存储恢复开关状态
    const savedState = localStorage.getItem('translateImagesEnabled');
    if (savedState !== null) {
        $switch.prop('checked', savedState === 'true');
    }

    // 监听开关变化
    $switch.on('change', function() {
        const isEnabled = $(this).is(':checked');

        // 保存状态到本地存储
        localStorage.setItem('translateImagesEnabled', isEnabled.toString());

        // 显示状态变化提示
        if (typeof layer !== 'undefined') {
            const message = isEnabled ? '已启用图片翻译' : '已禁用图片翻译（适用于产品图等无需翻译的图片）';
            layer.msg(message, {icon: 1, time: 2000});
        }

        // 如果当前有翻译结果且开关状态改变，提示用户重新翻译
        if (!isTranslating && hasTranslationResult()) {
            setTimeout(() => {
                if (typeof layer !== 'undefined') {
                    layer.confirm('图片翻译设置已更改，是否立即重新翻译以应用新设置？', {
                        icon: 3,
                        title: '设置更改提示',
                        btn: ['立即重新翻译', '稍后手动翻译']
                    }, function(index) {
                        // 点击确定，立即重新翻译
                        layer.close(index);
                        refreshTranslation();
                    }, function(index) {
                        // 点击取消，只显示提示
                        layer.close(index);
                        layer.msg('请在需要时手动点击"强制重新翻译"按钮', {icon: 0, time: 3000});
                    });
                }
            }, 2500);
        }
    });
}

// 检查是否有翻译结果
function hasTranslationResult() {
    const $translatedContent = $('#translatedContent');
    return $translatedContent.length > 0 &&
           !$translatedContent.find('.text-muted').length &&
           !$translatedContent.find('#loadingText').length;
}

// 单独翻译功能
function translateSingle(type) {
    // 检查是否正在翻译
    if (isTranslating) {
        if (typeof layer !== 'undefined') {
            layer.msg('翻译正在进行中，请稍候...', {icon: 0, time: 2000});
        }
        return;
    }

    // 获取原始内容
    let originalContent = '';
    let title = '';

    switch(type) {
        case 'subject':
            originalContent = $('#originalSubject').text().trim();
            title = '单独翻译标题';
            if (!originalContent || originalContent === '无标题') {
                layer.msg('没有可翻译的标题内容', {icon: 0, time: 2000});
                return;
            }
            break;
        case 'content':
            originalContent = $('#originalContentText').text().trim();
            title = '单独翻译内容';
            if (!originalContent || originalContent === '无内容' || originalContent.includes('加载中')) {
                layer.msg('没有可翻译的内容', {icon: 0, time: 2000});
                return;
            }
            break;
        case 'images':
            title = '单独翻译图片';
            // 图片翻译需要特殊处理
            break;
        default:
            layer.msg('未知的翻译类型', {icon: 2, time: 2000});
            return;
    }

    // 打开翻译配置弹窗
    openTranslationDialog(type, title, originalContent);
}

// 打开翻译配置弹窗
function openTranslationDialog(type, title, originalContent) {
    const dialogHtml = `
        <div class="translation-dialog">
            <form id="translationForm">
                <div class="form-group">
                    <label for="promptSelect">选择提示词：</label>
                    <select class="form-control" id="promptSelect" onchange="onPromptChange()">
                        <option value="">请选择提示词...</option>
                        <option value="custom">自定义提示词</option>
                    </select>
                </div>

                <div class="form-group" id="customPromptGroup" style="display: none;">
                    <label for="customPrompt">自定义提示词：</label>
                    <textarea class="form-control" id="customPrompt" rows="4"
                              placeholder="请输入自定义提示词..."></textarea>
                </div>

                <div class="form-group">
                    <label for="providerSelect">选择AI提供商：</label>
                    <select class="form-control" id="providerSelect" onchange="onProviderChange()">
                        <option value="">请选择提供商...</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="modelSelect">选择AI模型：</label>
                    <select class="form-control" id="modelSelect">
                        <option value="">请先选择提供商...</option>
                    </select>
                </div>

                ${type !== 'images' ? `
                <div class="form-group">
                    <label>原始内容预览：</label>
                    <div class="border p-2 bg-light" style="max-height: 150px; overflow-y: auto;">
                        ${originalContent}
                    </div>
                </div>
                ` : ''}

                <div class="form-group text-right">
                    <button type="button" class="btn btn-secondary mr-2" onclick="layer.closeAll()">取消</button>
                    <button type="button" class="btn btn-primary" onclick="executeSingleTranslation('${type}')">
                        <i class="fa fa-language"></i> 开始翻译
                    </button>
                </div>
            </form>
        </div>
    `;

    // 使用layer弹窗
    if (typeof layer !== 'undefined') {
        layer.open({
            type: 1,
            title: title,
            content: dialogHtml,
            area: ['600px', '500px'],
            success: function(layero, index) {
                // 加载提示词和模型数据
                loadPromptsAndModels();
            }
        });
    }
}

// 加载提示词和模型数据
async function loadPromptsAndModels() {
    try {
        // 加载提示词
        const promptResponse = await $.get('admin/aiPrompt/list');
        if (promptResponse.state === 'ok' && promptResponse.data) {
            const $promptSelect = $('#promptSelect');
            promptResponse.data.forEach(prompt => {
                if (prompt.enable) {
                    $promptSelect.append(`<option value="${prompt.id}" data-system="${prompt.systemContent}" data-user="${prompt.userContent}">${prompt.remark || prompt.key}</option>`);
                }
            });
        }

        // 加载AI提供商
        const providerResponse = await $.get('admin/llmProvider/list');
        if (providerResponse.state === 'ok' && providerResponse.data) {
            const $providerSelect = $('#providerSelect');
            providerResponse.data.forEach(provider => {
                if (provider.status) {
                    $providerSelect.append(`<option value="${provider.id}">${provider.name}</option>`);
                }
            });
        }
    } catch (error) {
        console.error('加载提示词和模型数据失败', error);
        if (typeof layer !== 'undefined') {
            layer.msg('加载配置数据失败', {icon: 2, time: 2000});
        }
    }
}

// 提示词选择变化
function onPromptChange() {
    const $promptSelect = $('#promptSelect');
    const $customPromptGroup = $('#customPromptGroup');

    if ($promptSelect.val() === 'custom') {
        $customPromptGroup.show();
    } else {
        $customPromptGroup.hide();
    }
}

// 提供商选择变化
async function onProviderChange() {
    const providerId = $('#providerSelect').val();
    const $modelSelect = $('#modelSelect');

    // 清空模型选择
    $modelSelect.empty().append('<option value="">请选择模型...</option>');

    if (!providerId) {
        return;
    }

    try {
        // 加载该提供商的模型
        const response = await $.get(`admin/llmModel/listByProvider?providerId=${providerId}`);
        if (response.state === 'ok' && response.data) {
            response.data.forEach(model => {
                if (model.status) {
                    $modelSelect.append(`<option value="${model.id}" data-identifier="${model.modelIdentifier}">${model.modelName}</option>`);
                }
            });
        }
    } catch (error) {
        console.error('加载模型数据失败', error);
        if (typeof layer !== 'undefined') {
            layer.msg('加载模型数据失败', {icon: 2, time: 2000});
        }
    }
}

// 执行单独翻译
async function executeSingleTranslation(type) {
    // 验证表单
    const promptId = $('#promptSelect').val();
    const customPrompt = $('#customPrompt').val();
    const providerId = $('#providerSelect').val();
    const modelId = $('#modelSelect').val();

    if (!promptId && !customPrompt) {
        layer.msg('请选择提示词或输入自定义提示词', {icon: 0, time: 2000});
        return;
    }

    if (!providerId) {
        layer.msg('请选择AI提供商', {icon: 0, time: 2000});
        return;
    }

    if (!modelId) {
        layer.msg('请选择AI模型', {icon: 0, time: 2000});
        return;
    }

    // 获取原始内容
    let originalContent = '';
    switch(type) {
        case 'subject':
            originalContent = $('#originalSubject').text().trim();
            break;
        case 'content':
            originalContent = $('#originalContentText').text().trim();
            break;
        case 'images':
            // 图片翻译需要特殊处理
            break;
    }

    // 构建请求参数
    const params = {
        emailId: '#(emailId??)',
        type: type,
        originalContent: originalContent,
        promptId: promptId === 'custom' ? null : promptId,
        customPrompt: promptId === 'custom' ? customPrompt : null,
        providerId: providerId,
        modelId: modelId
    };

    // 显示加载状态
    const loadingIndex = layer.load(1, {shade: [0.3, '#000']});

    try {
        const response = await $.post('admin/emailMessages/translateSingle', params);

        layer.close(loadingIndex);

        if (response.state === 'ok') {
            // 更新对应的翻译结果
            updateSingleTranslationResult(type, response.data);

            // 关闭弹窗
            layer.closeAll();

            // 显示成功消息
            layer.msg('翻译完成！', {icon: 1, time: 2000});
        } else {
            layer.msg('翻译失败：' + (response.msg || '未知错误'), {icon: 2, time: 3000});
        }
    } catch (error) {
        layer.close(loadingIndex);
        console.error('单独翻译失败', error);
        layer.msg('翻译请求失败', {icon: 2, time: 3000});
    }
}

// 更新单独翻译结果
function updateSingleTranslationResult(type, translationData) {
    switch(type) {
        case 'subject':
            if (translationData.subjectTranslated) {
                $('#translatedSubject').html(`
                    <div class="translation-status completed mb-2">
                        <i class="fa fa-check-circle"></i> 标题翻译完成
                    </div>
                    <div>${escapeHtml(translationData.subjectTranslated)}</div>
                `);
            }
            break;
        case 'content':
            if (translationData.contentTranslated) {
                $('#translatedContentText').html(`
                    <div class="translation-status completed mb-2">
                        <i class="fa fa-check-circle"></i> 内容翻译完成
                    </div>
                    <div>${escapeHtml(translationData.contentTranslated)}</div>
                `);
            }
            break;
        case 'images':
            if (translationData.imageTranslations) {
                displayImageTranslations(translationData.imageTranslations);
            }
            break;
    }

    // 更新完整翻译内容
    updateCombinedTranslation();
}

// 编辑翻译内容
function editTranslation(type) {
    let currentContent = '';
    let title = '';

    switch(type) {
        case 'subject':
            const $subjectDiv = $('#translatedSubject');
            currentContent = $subjectDiv.find('div:last').text() || $subjectDiv.text();
            title = '编辑标题翻译';
            if (!currentContent || currentContent.includes('暂无翻译')) {
                layer.msg('没有可编辑的标题翻译', {icon: 0, time: 2000});
                return;
            }
            break;
        case 'content':
            const $contentDiv = $('#translatedContentText');
            currentContent = $contentDiv.find('div:last').text() || $contentDiv.text();
            title = '编辑内容翻译';
            if (!currentContent || currentContent.includes('暂无翻译')) {
                layer.msg('没有可编辑的内容翻译', {icon: 0, time: 2000});
                return;
            }
            break;
        case 'images':
            title = '编辑图片翻译';
            // 图片翻译编辑需要特殊处理
            editImageTranslations();
            return;
        default:
            layer.msg('未知的编辑类型', {icon: 2, time: 2000});
            return;
    }

    // 打开编辑弹窗
    openEditDialog(type, title, currentContent);
}

// 打开编辑弹窗
function openEditDialog(type, title, currentContent) {
    const dialogHtml = `
        <div class="edit-dialog">
            <form id="editForm">
                <div class="form-group">
                    <label for="editContent">翻译内容：</label>
                    <textarea class="form-control" id="editContent" rows="8"
                              placeholder="请输入翻译内容...">${escapeHtml(currentContent)}</textarea>
                </div>

                <div class="form-group text-right">
                    <button type="button" class="btn btn-secondary mr-2" onclick="layer.closeAll()">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveEditedTranslation('${type}')">
                        <i class="fa fa-save"></i> 保存
                    </button>
                </div>
            </form>
        </div>
    `;

    // 使用layer弹窗
    if (typeof layer !== 'undefined') {
        layer.open({
            type: 1,
            title: title,
            content: dialogHtml,
            area: ['500px', '400px']
        });
    }
}

// 保存编辑的翻译内容
async function saveEditedTranslation(type) {
    const editedContent = $('#editContent').val().trim();

    if (!editedContent) {
        layer.msg('翻译内容不能为空', {icon: 0, time: 2000});
        return;
    }

    // 显示加载状态
    const loadingIndex = layer.load(1, {shade: [0.3, '#000']});

    try {
        const params = {
            emailId: '#(emailId??)',
            type: type,
            translatedContent: editedContent
        };

        const response = await $.post('admin/emailMessages/saveEditedTranslation', params);

        layer.close(loadingIndex);

        if (response.state === 'ok') {
            // 更新显示
            updateEditedTranslationDisplay(type, editedContent);

            // 关闭弹窗
            layer.closeAll();

            // 显示成功消息
            layer.msg('保存成功！', {icon: 1, time: 2000});
        } else {
            layer.msg('保存失败：' + (response.msg || '未知错误'), {icon: 2, time: 3000});
        }
    } catch (error) {
        layer.close(loadingIndex);
        console.error('保存编辑内容失败', error);
        layer.msg('保存请求失败', {icon: 2, time: 3000});
    }
}

// 更新编辑后的翻译显示
function updateEditedTranslationDisplay(type, editedContent) {
    switch(type) {
        case 'subject':
            $('#translatedSubject').html(`
                <div class="translation-status completed mb-2">
                    <i class="fa fa-edit"></i> 标题翻译（已编辑）
                </div>
                <div>${escapeHtml(editedContent)}</div>
            `);
            break;
        case 'content':
            $('#translatedContentText').html(`
                <div class="translation-status completed mb-2">
                    <i class="fa fa-edit"></i> 内容翻译（已编辑）
                </div>
                <div>${escapeHtml(editedContent)}</div>
            `);
            break;
    }

    // 更新完整翻译内容
    updateCombinedTranslation();
}

// 更新完整翻译内容
function updateCombinedTranslation() {
    const subjectTranslated = $('#translatedSubject').find('div:last').text() || '';
    const contentTranslated = $('#translatedContentText').find('div:last').text() || '';

    let combinedContent = '';
    if (subjectTranslated && !subjectTranslated.includes('暂无翻译')) {
        combinedContent += '【翻译标题】\n' + subjectTranslated + '\n\n';
    }
    if (contentTranslated && !contentTranslated.includes('暂无翻译')) {
        combinedContent += '【翻译内容】\n' + contentTranslated + '\n\n';
    }

    // 添加图片翻译内容
    const $imageContainer = $('#imageTranslationContainer');
    if ($imageContainer.find('.image-translation-item').length > 0) {
        $imageContainer.find('.image-translation-item').each(function(index) {
            const imageContent = $(this).find('.translation-content').text();
            if (imageContent) {
                combinedContent += `【图片翻译 ${index + 1}】\n${imageContent}\n\n`;
            }
        });
    }

    if (combinedContent) {
        $('#translatedContent').html(`
            <div class="translation-status completed mb-3">
                <i class="fa fa-check-circle"></i> 翻译完成
            </div>
            <div>${combinedContent}</div>
        `);
    }
}

// 编辑图片翻译
function editImageTranslations() {
    const $imageContainer = $('#imageTranslationContainer');
    const imageItems = $imageContainer.find('.image-translation-item');

    if (imageItems.length === 0) {
        layer.msg('没有可编辑的图片翻译', {icon: 0, time: 2000});
        return;
    }

    let dialogHtml = `
        <div class="edit-images-dialog">
            <form id="editImagesForm">
    `;

    imageItems.each(function(index) {
        const imagePath = $(this).data('image-path') || '';
        const translationContent = $(this).find('.translation-content').text() || '';

        dialogHtml += `
            <div class="form-group">
                <label>图片 ${index + 1}：</label>
                <small class="text-muted d-block">${imagePath}</small>
                <textarea class="form-control image-translation-textarea"
                          data-index="${index}"
                          rows="3"
                          placeholder="请输入图片翻译内容...">${escapeHtml(translationContent)}</textarea>
            </div>
        `;
    });

    dialogHtml += `
                <div class="form-group text-right">
                    <button type="button" class="btn btn-secondary mr-2" onclick="layer.closeAll()">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveEditedImageTranslations()">
                        <i class="fa fa-save"></i> 保存
                    </button>
                </div>
            </form>
        </div>
    `;

    // 使用layer弹窗
    if (typeof layer !== 'undefined') {
        layer.open({
            type: 1,
            title: '编辑图片翻译',
            content: dialogHtml,
            area: ['600px', '500px']
        });
    }
}

// 保存编辑的图片翻译
async function saveEditedImageTranslations() {
    const imageTranslations = [];

    $('.image-translation-textarea').each(function() {
        const index = $(this).data('index');
        const content = $(this).val().trim();
        const $originalItem = $('#imageTranslationContainer .image-translation-item').eq(index);
        const imagePath = $originalItem.data('image-path') || '';

        if (content) {
            imageTranslations.push({
                imagePath: imagePath,
                translatedContent: content,
                order: index + 1
            });
        }
    });

    // 显示加载状态
    const loadingIndex = layer.load(1, {shade: [0.3, '#000']});

    try {
        const params = {
            emailId: '#(emailId??)',
            type: 'images',
            imageTranslations: JSON.stringify(imageTranslations)
        };

        const response = await $.post('admin/emailMessages/saveEditedTranslation', params);

        layer.close(loadingIndex);

        if (response.state === 'ok') {
            // 更新图片翻译显示
            displayImageTranslations(imageTranslations);

            // 关闭弹窗
            layer.closeAll();

            // 显示成功消息
            layer.msg('保存成功！', {icon: 1, time: 2000});

            // 更新完整翻译内容
            updateCombinedTranslation();
        } else {
            layer.msg('保存失败：' + (response.msg || '未知错误'), {icon: 2, time: 3000});
        }
    } catch (error) {
        layer.close(loadingIndex);
        console.error('保存图片翻译失败', error);
        layer.msg('保存请求失败', {icon: 2, time: 3000});
    }
}

$(function() {
    console.log('#(emailId??)');

    // 初始化工具提示
    $('[data-toggle="tooltip"]').tooltip();

    // 初始化图片翻译开关事件
    initTranslateImagesSwitch();

    // 页面加载时先加载原始邮件信息
    loadOriginalEmailInfo();
    // 然后检查是否有翻译记录
    checkExistingTranslation();
    
    // 为截图添加点击事件
    $(document).on('click', '.screenshot-preview', function() {
        showImagePreview($(this).attr('src'));
    });
    
    // 为缩略图添加点击事件
    $(document).on('click', '.thumbnail', function() {
        const index = $(this).data('index');
        showScreenshot(index);
        $('.thumbnail').removeClass('active');
        $(this).addClass('active');
    });
    
    // 监听标签页切换事件
    $('#screenshot-tab').on('shown.bs.tab', function (e) {
        // 如果切换到截图标签，确保轮播正常显示
        if (allScreenshots.length > 0) {
            $('#screenshotCarousel').carousel('cycle');
        }
    });
    
    // 监听原附件图片标签切换事件
    $('#attachments-tab').on('shown.bs.tab', function (e) {
        // 如果切换到原附件图片标签，加载邮件附件图片
        if (allAttachmentImages.length === 0) {
            loadEmailAttachmentImages();
        } else if (allAttachmentImages.length > 0) {
            $('#attachmentImagesCarousel').carousel('cycle');
        }
    });
    
    // 监听轮播切换事件
    $('#screenshotCarousel').on('slid.bs.carousel', function () {
        // 更新当前截图索引
        currentScreenshotIndex = $('.carousel-item.active').index();
        updateScreenshotCounter();
        
        // 更新缩略图选中状态
        $('.thumbnail').removeClass('active');
        $(`.thumbnail[data-index="${currentScreenshotIndex}"]`).addClass('active');
    });
    
    // 监听附件图片轮播切换事件
    $('#attachmentImagesCarousel').on('slid.bs.carousel', function () {
        // 更新当前附件图片索引
        currentAttachmentImageIndex = $('#attachmentImagesCarousel .carousel-item.active').index();
        updateAttachmentImageCounter();

        // 更新缩略图选中状态
        $('.attachment-thumbnail').removeClass('active');
        $(`.attachment-thumbnail[data-index="${currentAttachmentImageIndex}"]`).addClass('active');
    });

    // 添加键盘快捷键支持
    $(document).on('keydown', function(e) {
        // Ctrl + R 或 F5 触发强制重新翻译
        if ((e.ctrlKey && e.key === 'r') || e.key === 'F5') {
            e.preventDefault();
            if (!isTranslating) {
                refreshTranslation();
            }
        }

        // Ctrl + T 触发翻译（如果没有翻译记录）
        if (e.ctrlKey && e.key === 't') {
            e.preventDefault();
            if (!isTranslating && $('#translatedContent').find('.text-muted').length > 0) {
                refreshTranslation();
            }
        }
    });
});

// 加载原始邮件信息
async function loadOriginalEmailInfo() {
    try {
        const response = await $.get('admin/emailMessages/getEmailInfo', {
            emailId: '#(emailId??)'
        });

        if (response.state === 'ok' && response.data) {
            const email = response.data;

            // 显示原始标题
            if (email.subject) {
                $('#originalSubject').html(escapeHtml(email.subject));
            } else {
                $('#originalSubject').html('<div class="text-muted">无标题</div>');
            }

            // 显示原始内容
            let originalContent = email.contentHtml || email.contentText || email.content || '';
            if (originalContent) {
                // 如果是HTML内容，需要提取纯文本或直接显示
                $('#originalContentText').html(originalContent);
            } else {
                $('#originalContentText').html('<div class="text-muted">无内容</div>');
            }
        }
    } catch (error) {
        console.error('加载原始邮件信息失败', error);
        $('#originalSubject').html('<div class="text-muted">加载失败</div>');
        $('#originalContentText').html('<div class="text-muted">加载失败</div>');
    }
}

// 检查是否有已存在的翻译记录
async function checkExistingTranslation() {
    try {
        const response = await $.get('admin/emailMessages/getEmailTranslation', {
            emailId: '#(emailId??)'
        });
        console.dir(response.data);
        if (response.state === 'ok') {
            // 已有翻译记录，直接显示
            $('#loadingText').hide();
            $('#subjectLoadingText').hide();

            // 显示分离的翻译内容
            displaySeparateTranslations(response.data);

            // 显示截图（如果有）
            if (response.data && response.data.screenshotPath) {
                // 显示截图标签
                $('#screenshot-tab').removeClass('d-none');

                // 加载所有截图
                loadAllScreenshots(response.data.screenshotPath);
            } else {
                // 如果没有截图，隐藏截图标签
                $('#screenshot-tab').addClass('d-none');
            }

            // 确保原附件图片标签始终可见
            $('#attachments-tab').removeClass('d-none');
        } else {
            // 没有翻译记录，显示提示
            $('#loadingText').html('<div class="text-muted">暂无翻译，请点击"翻译"按钮进行翻译</div>');
            // 隐藏截图标签
            $('#screenshot-tab').addClass('d-none');
            // 确保原附件图片标签始终可见
            $('#attachments-tab').removeClass('d-none');
        }
    } catch (error) {
        $('#loadingText').html('<div class="text-muted">暂无翻译，请点击"翻译"按钮进行翻译</div>');
        console.error('获取翻译记录失败', error);
        // 隐藏截图标签
        $('#screenshot-tab').addClass('d-none');
        // 确保原附件图片标签始终可见
        $('#attachments-tab').removeClass('d-none');
    }
}

// 翻译内容
async function translateContent(forceTranslate = false) {
    $('#loadingText').show();
    $('#translatedContent').html('<div class="text-center" id="loadingText"><i class="fa fa-spinner fa-spin"></i> 翻译中...</div>');

    try {
        const response = await $.post('admin/emailMessages/translateEmail', {
            emailId: '#(emailId??)',
            forceTranslate: forceTranslate,
            translateImages: $('#translateImagesSwitch').is(':checked')
        });

        if (response.state === 'ok') {
            $('#loadingText').hide();
            $('#subjectLoadingText').hide();

            // 显示分离的翻译内容
            displaySeparateTranslations(response.data);

            // 显示截图（如果有）
            if (response.data && response.data.screenshotPath) {
                // 显示截图标签
                $('#screenshot-tab').removeClass('d-none');

                // 加载所有截图
                loadAllScreenshots(response.data.screenshotPath);
            } else {
                // 如果没有截图，隐藏截图标签
                $('#screenshot-tab').addClass('d-none');
            }

            // 确保原附件图片标签始终可见
            $('#attachments-tab').removeClass('d-none');
        } else {
            $('#loadingText').html('<div class="text-danger">翻译失败：' + response.msg + '</div>');
        }
    } catch (error) {
        $('#loadingText').html('<div class="text-danger">翻译请求失败</div>');
        console.error('翻译请求失败', error);
    }
}

// 带进度条的翻译内容函数
async function translateContentWithProgress(forceTranslate = false) {
    // 检查是否已在翻译中
    if (isTranslating && !forceTranslate) {
        if (typeof layer !== 'undefined') {
            layer.msg('翻译正在进行中，请稍候...', {icon: 0, time: 2000});
        }
        return;
    }

    // 设置翻译状态
    isTranslating = true;

    // 显示翻译进度
    $('#loadingText').show();
    $('#translatedContent').html('<div class="text-center" id="loadingText"><i class="fa fa-spinner fa-spin"></i> 翻译中...</div>');

    // 显示进度提示
    showTranslationProgress();

    try {
        const response = await $.post('admin/emailMessages/translateEmail', {
            emailId: '#(emailId??)',
            forceTranslate: forceTranslate,
            translateImages: $('#translateImagesSwitch').is(':checked')
        });

        if (response.state === 'ok') {
            $('#loadingText').hide();
            $('#subjectLoadingText').hide();

            // 显示分离的翻译内容
            displaySeparateTranslations(response.data);

            // 显示截图（如果有）
            if (response.data && response.data.screenshotPath) {
                // 显示截图标签
                $('#screenshot-tab').removeClass('d-none');

                // 加载所有截图
                loadAllScreenshots(response.data.screenshotPath);
            } else {
                // 如果没有截图，隐藏截图标签
                $('#screenshot-tab').addClass('d-none');
            }

            // 确保原附件图片标签始终可见
            $('#attachments-tab').removeClass('d-none');

            // 显示成功消息
            if (typeof layer !== 'undefined') {
                layer.msg('翻译完成！', {icon: 1, time: 2000});
            }

            // 重置翻译状态
            isTranslating = false;
        } else {
            // 显示错误状态
            $('#translatedSubject').html('<div class="translation-status error"><i class="fa fa-exclamation-triangle"></i> 标题翻译失败</div>');
            $('#translatedContentText').html('<div class="translation-status error"><i class="fa fa-exclamation-triangle"></i> 内容翻译失败</div>');
            $('#imageTranslationContainer').html('<div class="translation-status error"><i class="fa fa-exclamation-triangle"></i> 图片翻译失败</div>');

            $('#loadingText').html(`
                <div class="alert alert-danger text-center">
                    <i class="fa fa-exclamation-triangle"></i> 翻译失败：${response.msg || '未知错误'}
                    <br><small class="mt-2">请检查网络连接或稍后重试</small>
                </div>
            `);

            if (typeof layer !== 'undefined') {
                layer.msg('翻译失败：' + (response.msg || '未知错误'), {icon: 2, time: 4000});
            }

            // 重置翻译状态
            isTranslating = false;
        }
    } catch (error) {
        // 显示错误状态
        $('#translatedSubject').html('<div class="translation-status error"><i class="fa fa-exclamation-triangle"></i> 标题翻译失败</div>');
        $('#translatedContentText').html('<div class="translation-status error"><i class="fa fa-exclamation-triangle"></i> 内容翻译失败</div>');
        $('#imageTranslationContainer').html('<div class="translation-status error"><i class="fa fa-exclamation-triangle"></i> 图片翻译失败</div>');

        $('#loadingText').html(`
            <div class="alert alert-danger text-center">
                <i class="fa fa-exclamation-triangle"></i> 翻译请求失败
                <br><small class="mt-2">请检查网络连接或稍后重试</small>
            </div>
        `);

        console.error('翻译请求失败', error);
        if (typeof layer !== 'undefined') {
            layer.msg('翻译请求失败，请检查网络连接', {icon: 2, time: 4000});
        }

        // 重置翻译状态
        isTranslating = false;
    }
}

// 显示分离的翻译内容
function displaySeparateTranslations(data) {
    if (!data) {
        return;
    }

    // 显示标题翻译
    if (data.subjectOriginal) {
        $('#originalSubject').html(escapeHtml(data.subjectOriginal));
    } else {
        $('#originalSubject').html('<div class="text-muted">无标题</div>');
    }

    if (data.subjectTranslated) {
        $('#translatedSubject').html(`
            <div class="translation-status completed mb-2">
                <i class="fa fa-check-circle"></i> 标题翻译完成
            </div>
            <div>${escapeHtml(data.subjectTranslated)}</div>
        `);
    } else {
        $('#translatedSubject').html('<div class="text-muted">暂无翻译</div>');
    }

    // 显示内容翻译
    if (data.contentOriginal) {
        $('#originalContentText').html(escapeHtml(data.contentOriginal));
    } else if (data.originalContent) {
        $('#originalContentText').html(escapeHtml(data.originalContent));
    } else {
        $('#originalContentText').html('<div class="text-muted">无内容</div>');
    }

    if (data.contentTranslated) {
        $('#translatedContentText').html(`
            <div class="translation-status completed mb-2">
                <i class="fa fa-check-circle"></i> 内容翻译完成
            </div>
            <div>${escapeHtml(data.contentTranslated)}</div>
        `);
    } else {
        $('#translatedContentText').html('<div class="text-muted">暂无翻译</div>');
    }

    // 显示图片翻译
    displayImageTranslations(data.imageTranslations);

    // 显示完整翻译（兼容性）
    if (data.translatedContent) {
        $('#translatedContent').html(`
            <div class="translation-status completed mb-3">
                <i class="fa fa-check-circle"></i> 翻译完成
            </div>
            <div>${data.translatedContent}</div>
        `);
    } else {
        // 如果没有完整翻译，构建一个
        let combinedContent = '';
        if (data.subjectTranslated) {
            combinedContent += '【翻译标题】\n' + data.subjectTranslated + '\n\n';
        }
        if (data.contentTranslated) {
            combinedContent += '【翻译内容】\n' + data.contentTranslated + '\n\n';
        }
        if (data.imageTranslations && data.imageTranslations.length > 0) {
            data.imageTranslations.forEach((img, index) => {
                combinedContent += `【图片翻译 ${index + 1}】\n图片路径: ${img.imagePath}\n翻译内容: ${img.translatedContent}\n\n`;
            });
        }

        if (combinedContent) {
            $('#translatedContent').html(`
                <div class="translation-status completed mb-3">
                    <i class="fa fa-check-circle"></i> 翻译完成
                </div>
                <div>${combinedContent}</div>
            `);
        } else {
            $('#translatedContent').html('<div class="text-muted">暂无翻译</div>');
        }
    }
}

// 显示图片翻译
function displayImageTranslations(imageTranslations) {
    const container = $('#imageTranslationContainer');
    const translateImages = $('#translateImagesSwitch').is(':checked');

    // 如果禁用了图片翻译，显示相应提示
    if (!translateImages) {
        container.html(`
            <div class="text-center" style="padding: 2rem; background-color: #f8f9fa; border-radius: 0.375rem;">
                <i class="fa fa-image fa-2x text-muted mb-2"></i>
                <p class="text-muted mb-1">图片翻译已禁用</p>
                <small class="text-muted">适用于产品图等无需翻译的图片内容</small>
            </div>
        `);
        return;
    }

    if (!imageTranslations || imageTranslations.length === 0) {
        container.html('<div class="text-center text-muted">暂无图片翻译</div>');
        return;
    }

    let html = '';
    imageTranslations.forEach((img, index) => {
        html += `
            <div class="image-translation-item">
                <div class="image-translation-header">
                    图片翻译 ${img.order || (index + 1)}
                </div>
                <div class="image-translation-content">
                    <div class="row">
                        <div class="col-md-4">
                            <h6 class="text-muted">图片预览</h6>
                            <img src="/common/file?filePath=${encodeURIComponent(img.imagePath)}"
                                 class="image-preview"
                                 alt="图片 ${img.order || (index + 1)}"
                                 onerror="this.src='assets/img/noimg.png'">
                            <div class="mt-2 small text-muted">
                                路径: ${escapeHtml(img.imagePath)}
                            </div>
                        </div>
                        <div class="col-md-8">
                            <h6 class="text-muted">翻译内容</h6>
                            <div class="border p-2 bg-white">
                                ${escapeHtml(img.translatedContent || '暂无翻译')}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    container.html(html);
}

// 加载所有截图
async function loadAllScreenshots(mainScreenshotPath) {
    // 显示加载中
    $('#screenshotLoading').show();
    $('#screenshotCarouselInner').html('<div class="text-center py-3" id="screenshotLoading"><i class="fa fa-spinner fa-spin"></i> 正在加载截图...</div>');
    
    try {
        // 调用后端API获取同一目录下的所有截图
        const response = await $.get('admin/emailMessages/getAllScreenshots', {
            screenshotPath: mainScreenshotPath
        });
        
        if (response.state === 'ok' && Array.isArray(response.data)) {
            // 清空当前截图
            allScreenshots = [];
            $('#screenshotCarouselInner').empty();
            $('#thumbnailContainer').empty();
            
            // 处理返回的截图列表
            if (response.data.length > 0) {
                allScreenshots = response.data;
                
                // 创建轮播项
                allScreenshots.forEach((screenshot, index) => {
                    const isActive = index === 0 ? 'active' : '';
                    // 从路径中提取文件名
                    const fileName = screenshot.path.split('/').pop().split('\\').pop();
                    const carouselItem = `
                        <div class="carousel-item ${isActive}">
                            <div class="screenshot-container">
                                <div class="screenshot-filename text-center mb-2">
                                    <small class="text-muted">${fileName}</small>
                                </div>
                                <img src="common/file?filePath=${screenshot.path}" 
                                    class="screenshot-preview ${index === 0 ? 'screenshot-small' : ''}" 
                                    alt="截图 ${index + 1}" 
                                    data-index="${index}">
                            </div>
                        </div>
                    `;
                    $('#screenshotCarouselInner').append(carouselItem);
                    
                    // 创建缩略图
                    const thumbnail = `
                        <div class="col-auto text-center">
                            <img src="common/file?filePath=${screenshot.path}" 
                                class="thumbnail ${index === 0 ? 'active' : ''}" 
                                alt="缩略图 ${index + 1}" 
                                data-index="${index}">
                            <div class="thumbnail-filename">
                                <small class="text-muted">${fileName}</small>
                            </div>
                        </div>
                    `;
                    $('#thumbnailContainer').append(thumbnail);
                });
                
                // 更新截图计数
                currentScreenshotIndex = 0;
                updateScreenshotCounter();
                
                // 初始化轮播
                $('#screenshotCarousel').carousel({
                    interval: false
                });
                
                // 隐藏加载中提示
                $('#screenshotLoading').hide();
            } else {
                // 没有截图
                $('#screenshotCarouselInner').html('<div class="alert alert-info">没有找到截图</div>');
                $('#thumbnailContainer').html('');
                $('#screenshotCounter').text('0/0');
            }
        } else {
            // API返回错误
            $('#screenshotCarouselInner').html(`<div class="alert alert-danger">加载截图失败: ${response.msg || '未知错误'}</div>`);
            $('#thumbnailContainer').html('');
            $('#screenshotCounter').text('0/0');
        }
    } catch (error) {
        console.error('加载截图失败:', error);
        $('#screenshotCarouselInner').html('<div class="alert alert-danger">加载截图失败: ' + (error.message || '网络错误') + '</div>');
        $('#thumbnailContainer').html('');
        $('#screenshotCounter').text('0/0');
    }
}

// 更新截图计数
function updateScreenshotCounter() {
    if (allScreenshots.length > 0) {
        $('#screenshotCounter').text(`${currentScreenshotIndex + 1}/${allScreenshots.length}`);
    } else {
        $('#screenshotCounter').text('0/0');
    }
}

// 显示指定索引的截图
function showScreenshot(index) {
    if (index >= 0 && index < allScreenshots.length) {
        $('#screenshotCarousel').carousel(index);
        currentScreenshotIndex = index;
        updateScreenshotCounter();
    }
}

// 显示翻译进度
function showTranslationProgress() {
    // 检查是否启用图片翻译
    const translateImages = $('#translateImagesSwitch').is(':checked');

    // 更新各个标签页的加载状态
    $('#translatedSubject').html('<div class="translation-status translating"><i class="fa fa-spinner fa-spin"></i> 正在翻译标题...</div>');
    $('#translatedContentText').html('<div class="translation-status translating"><i class="fa fa-spinner fa-spin"></i> 正在翻译内容...</div>');

    if (translateImages) {
        $('#imageTranslationContainer').html('<div class="translation-status translating"><i class="fa fa-spinner fa-spin"></i> 正在翻译图片...</div>');
    } else {
        $('#imageTranslationContainer').html('<div class="translation-status" style="background-color: #f8f9fa; color: #6c757d;"><i class="fa fa-info-circle"></i> 已跳过图片翻译</div>');
    }

    // 显示详细的进度信息
    const progressSteps = translateImages ? [
        '正在分析邮件内容...',
        '正在提取文本信息...',
        '正在处理图片附件...',
        '正在调用翻译服务...',
        '正在生成翻译结果...',
        '正在保存翻译数据...'
    ] : [
        '正在分析邮件内容...',
        '正在提取文本信息...',
        '正在跳过图片处理...',
        '正在调用翻译服务...',
        '正在生成翻译结果...',
        '正在保存翻译数据...'
    ];

    let currentStep = 0;
    let progress = 0;

    const progressInterval = setInterval(() => {
        // 更新进度百分比
        progress += Math.random() * 15 + 5; // 每次增加5-20%
        if (progress >= 95) {
            progress = 95; // 最多到95%，等待实际完成
        }

        // 更新步骤
        if (currentStep < progressSteps.length - 1 && progress > (currentStep + 1) * 15) {
            currentStep++;
        }

        // 更新显示
        const progressText = Math.round(progress);
        const currentStepText = progressSteps[currentStep] || '正在完成翻译...';

        $('#loadingText').html(`
            <div class="translation-progress text-center">
                <div class="mb-2">
                    <i class="fa fa-spinner fa-spin"></i> ${currentStepText}
                </div>
                <div class="progress mb-2" style="height: 6px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar"
                         style="width: ${progressText}%"
                         aria-valuenow="${progressText}"
                         aria-valuemin="0"
                         aria-valuemax="100">
                    </div>
                </div>
                <small class="text-light">${progressText}% 完成</small>
            </div>
        `);

        if (progress >= 95) {
            clearInterval(progressInterval);
        }
    }, 600);

    // 保存interval ID以便后续清理
    window.translationProgressInterval = progressInterval;
}

// 刷新翻译（强制重新翻译）
function refreshTranslation() {
    // 防止重复点击
    if (isTranslating) {
        if (typeof layer !== 'undefined') {
            layer.msg('翻译正在进行中，请稍候...', {icon: 0, time: 2000});
        }
        return;
    }

    const $btn = $('#refreshTranslationBtn');
    const $icon = $('#refreshIcon');
    const $text = $('#refreshText');
    const $spinner = $('#refreshSpinner');
    const $switch = $('#translateImagesSwitch');

    // 设置翻译状态
    isTranslating = true;

    // 禁用按钮和开关并显示进度条
    $btn.prop('disabled', true);
    $switch.prop('disabled', true);
    $icon.addClass('d-none');
    $text.text('翻译中...');
    $spinner.removeClass('d-none');

    // 调用翻译函数
    translateContentWithProgress(true).finally(() => {
        // 清理进度interval
        if (window.translationProgressInterval) {
            clearInterval(window.translationProgressInterval);
            window.translationProgressInterval = null;
        }

        // 恢复按钮和开关状态
        $btn.prop('disabled', false);
        $switch.prop('disabled', false);
        $icon.removeClass('d-none');
        $text.text('强制重新翻译');
        $spinner.addClass('d-none');

        // 重置翻译状态
        isTranslating = false;
    });
}

// 显示图片预览
function showImagePreview(src) {
    if (!src) return;
    
    // 设置预览图片源
    $('#previewImage').attr('src', src);
    
    // 显示模态框
    $('#imagePreviewModal').modal('show');
}

// 切换截图大小
function toggleScreenshotSize() {
    const $img = $('.carousel-item.active .screenshot-preview');
    if ($img.hasClass('screenshot-small')) {
        $img.removeClass('screenshot-small').addClass('screenshot-large');
    } else {
        $img.removeClass('screenshot-large').addClass('screenshot-small');
    }
}

// 显示/隐藏缩略图画廊
function showThumbnailGallery() {
    const $gallery = $('#thumbnailGallery');
    if ($gallery.is(':visible')) {
        $gallery.slideUp();
    } else {
        $gallery.slideDown();
    }
}

// 加载邮件原附件图片
async function loadEmailAttachmentImages() {
    // 显示加载中
    $('#attachmentImagesLoading').show();
    $('#attachmentImagesCarouselInner').html('<div class="text-center py-3" id="attachmentImagesLoading"><i class="fa fa-spinner fa-spin"></i> 正在加载原附件图片...</div>');
    
    try {
        // 调用后端API获取邮件的图片附件
        const response = await $.get('admin/emailMessages/getEmailImageAttachments', {
            emailId: '#(emailId??)'
        });
        
        if (response.state === 'ok' && Array.isArray(response.data)) {
            // 清空当前附件图片
            allAttachmentImages = [];
            $('#attachmentImagesCarouselInner').empty();
            $('#attachmentThumbnailContainer').empty();
            
            // 处理返回的图片附件列表
            if (response.data.length > 0) {
                allAttachmentImages = response.data;
                
                // 创建轮播项
                allAttachmentImages.forEach((attachment, index) => {
                    const isActive = index === 0 ? 'active' : '';
                    const fileName = attachment.filename || attachment.name || `图片附件 ${index + 1}`;
                    const fileSize = attachment.size ? formatFileSize(attachment.size) : '';
                    
                    const carouselItem = `
                        <div class="carousel-item ${isActive}">
                            <div class="screenshot-container">
                                <div class="screenshot-filename text-center mb-2">
                                    <small class="text-muted">${fileName}</small>
                                    ${fileSize ? `<br><small class="text-info">${fileSize}</small>` : ''}
                                </div>
                                <img src="common/file?filePath=${attachment.path}" 
                                    class="screenshot-preview ${index === 0 ? 'screenshot-small' : ''}" 
                                    alt="附件图片 ${index + 1}" 
                                    data-index="${index}"
                                    onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaXoOazlemihOiniDwvdGV4dD48L3N2Zz4=';">
                            </div>
                        </div>
                    `;
                    $('#attachmentImagesCarouselInner').append(carouselItem);
                    
                    // 创建缩略图
                    const thumbnail = `
                        <div class="col-auto text-center">
                            <img src="common/file?filePath=${attachment.path}" 
                                class="attachment-thumbnail ${index === 0 ? 'active' : ''}" 
                                alt="缩略图 ${index + 1}" 
                                data-index="${index}"
                                onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iNjAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iI2RkZCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTAiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7ml6Dms5XpoobniYc8L3RleHQ+PC9zdmc+';">
                            <div class="thumbnail-filename">
                                <small class="text-muted">${fileName}</small>
                            </div>
                        </div>
                    `;
                    $('#attachmentThumbnailContainer').append(thumbnail);
                });
                
                // 更新附件图片计数
                currentAttachmentImageIndex = 0;
                updateAttachmentImageCounter();
                
                // 初始化轮播
                $('#attachmentImagesCarousel').carousel({
                    interval: false
                });
                
                // 隐藏加载中提示
                $('#attachmentImagesLoading').hide();
                
                // 为缩略图添加点击事件
                $('.attachment-thumbnail').on('click', function() {
                    const index = $(this).data('index');
                    showAttachmentImage(index);
                    $('.attachment-thumbnail').removeClass('active');
                    $(this).addClass('active');
                });
                
                // 显示原附件图片标签
                $('#attachments-tab').removeClass('d-none');
            } else {
                // 没有图片附件
                $('#attachmentImagesCarouselInner').html('<div class="alert alert-info">该邮件没有图片附件</div>');
                $('#attachmentThumbnailContainer').html('');
                $('#attachmentImagesCounter').text('0/0');
                // 保持原附件图片标签可见
                $('#attachments-tab').removeClass('d-none');
            }
        } else {
            // API返回错误
            $('#attachmentImagesCarouselInner').html(`<div class="alert alert-danger">加载图片附件失败: ${response.msg || '未知错误'}</div>`);
            $('#attachmentThumbnailContainer').html('');
            $('#attachmentImagesCounter').text('0/0');
            // 保持原附件图片标签可见
            $('#attachments-tab').removeClass('d-none');
        }
    } catch (error) {
        console.error('加载图片附件失败:', error);
        $('#attachmentImagesCarouselInner').html('<div class="alert alert-danger">加载图片附件失败: ' + (error.message || '网络错误') + '</div>');
        $('#attachmentThumbnailContainer').html('');
        $('#attachmentImagesCounter').text('0/0');
        // 保持原附件图片标签可见
        $('#attachments-tab').removeClass('d-none');
    }
}

// 更新附件图片计数
function updateAttachmentImageCounter() {
    if (allAttachmentImages.length > 0) {
        $('#attachmentImagesCounter').text(`${currentAttachmentImageIndex + 1}/${allAttachmentImages.length}`);
    } else {
        $('#attachmentImagesCounter').text('0/0');
    }
}

// 显示指定索引的附件图片
function showAttachmentImage(index) {
    if (index >= 0 && index < allAttachmentImages.length) {
        $('#attachmentImagesCarousel').carousel(index);
        currentAttachmentImageIndex = index;
        updateAttachmentImageCounter();
    }
}

// 切换附件图片大小
function toggleAttachmentImageSize() {
    const $img = $('#attachmentImagesCarousel .carousel-item.active .screenshot-preview');
    if ($img.hasClass('screenshot-small')) {
        $img.removeClass('screenshot-small').addClass('screenshot-large');
    } else {
        $img.removeClass('screenshot-large').addClass('screenshot-small');
    }
}

// 显示/隐藏附件缩略图画廊
function showAttachmentThumbnailGallery() {
    const $gallery = $('#attachmentThumbnailGallery');
    if ($gallery.is(':visible')) {
        $gallery.slideUp();
    } else {
        $gallery.slideDown();
    }
}

// 下载所有附件图片
function downloadAllAttachmentImages() {
    if (allAttachmentImages.length === 0) {
        layer.msg('没有可下载的图片附件', {icon: 2});
        return;
    }
    
    // 依次下载每个图片附件
    allAttachmentImages.forEach((attachment, index) => {
        setTimeout(() => {
            const link = document.createElement('a');
            link.href = `common/file?filePath=${attachment.path}`;
            link.download = attachment.filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }, index * 500); // 每个下载间隔500ms
    });
    
    layer.msg(`开始下载 ${allAttachmentImages.length} 个图片附件...`, {icon: 1});
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// HTML转义函数
function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function forwardEmail(emailId) {
    window.open('admin/emailMessages/forwardEmail/' + emailId + '?_jb_rqtype_=dialog', '_blank');
}

// 翻译转发
function forwardWithTranslation() {
    // 检查是否有翻译内容
    if ($('#translatedContent').find('#loadingText').length > 0 || $('#translatedContent').text().trim() === '') {
        layer.msg('请先完成翻译再进行转发', {icon: 2});
        return;
    }

    // 获取原文和译文内容
    const originalContent = $('#textContent').html();
    const translatedContent = $('#translatedContent').html();
    
    // 构建转发内容
    let forwardContent = '<div style="border-top: 1px solid #ccc; margin-top: 20px; padding-top: 10px;">';
    forwardContent += '<h3>原文：</h3>';
    forwardContent += '<div style="background-color: #f9f9f9; padding: 10px; border-radius: 5px;">' + originalContent + '</div>';
    forwardContent += '<h3 style="margin-top: 15px;">译文：</h3>';
    forwardContent += '<div style="background-color: #f0f8ff; padding: 10px; border-radius: 5px;">' + translatedContent + '</div>';
    forwardContent += '</div>';
    
    // 添加截图（如果有）
    if (allScreenshots && allScreenshots.length > 0) {
        forwardContent += '<h3 style="margin-top: 15px;">相关截图：</h3>';
        forwardContent += '<div style="display: flex; flex-wrap: wrap; gap: 10px;">';
        
        // 最多显示4张截图
        const maxScreenshots = Math.min(allScreenshots.length, 4);
        for (let i = 0; i < maxScreenshots; i++) {
            forwardContent += `<img src="common/file?filePath=${allScreenshots[i].path}" style="max-width: 300px; max-height: 200px; border: 1px solid #ddd;" />`;
        }
        
        if (allScreenshots.length > 4) {
            forwardContent += `<div style="padding: 10px; background-color: #eee; border-radius: 5px;">还有${allScreenshots.length - 4}张截图未显示...</div>`;
        }
        
        forwardContent += '</div>';
    }
    
    // 打开转发页面
    const emailId = '#(emailId??)';
    
    // 检测浏览器
    const isChrome = navigator.userAgent.indexOf('Chrome') > -1;
    const isIE = navigator.userAgent.indexOf('MSIE') > -1 || navigator.userAgent.indexOf('Trident') > -1;
    
    // 根据不同浏览器选择不同的全屏方式
    let windowFeatures;
    if (isIE) {
        // IE浏览器
        windowFeatures = 'fullscreen=yes,scrollbars=yes';
    } else {
        // Chrome和其他浏览器
        const width = window.screen.width;
        const height = window.screen.height;
        windowFeatures = `width=${width},height=${height},top=0,left=0,resizable=yes,scrollbars=yes,status=yes`;
    }
    
    const newWindow = window.open(`admin/emailMessages/forwardEmail/${emailId}?_jb_rqtype_=dialog&translationContent=${encodeURIComponent(forwardContent)}`, '_blank', windowFeatures);
    
    // 尝试最大化窗口
    if (newWindow) {
        try {
            // 移动到左上角并调整大小
            newWindow.moveTo(0, 0);
            newWindow.resizeTo(screen.width, screen.height);
            
            // 如果浏览器支持，尝试请求全屏
            if (newWindow.document && newWindow.document.documentElement && newWindow.document.documentElement.requestFullscreen) {
                newWindow.onload = function() {
                    try {
                        newWindow.document.documentElement.requestFullscreen();
                    } catch (e) {
                        console.log('无法进入全屏模式', e);
                    }
                };
            }
        } catch (e) {
            console.log('无法最大化窗口', e);
        }
    }
}
</script>
#end 