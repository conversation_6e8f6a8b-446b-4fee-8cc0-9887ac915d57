package cn.jbolt.admin.llmprovider;

import com.jfinal.plugin.activerecord.Page;
import cn.jbolt.extend.systemlog.ProjectSystemLogTargetType;
import cn.jbolt.core.service.base.JBoltBaseService;
import com.jfinal.kit.Kv;
import com.jfinal.kit.Okv;
import com.jfinal.kit.Ret;
import cn.jbolt.core.base.JBoltMsg;
import cn.jbolt.core.db.sql.Sql;
import cn.jbolt.common.model.LlmProvider;
/**
 * 大模型提供商表
 * @ClassName: LlmProviderService
 * @author: 总管理
 * @date: 2025-05-08 16:52  
 */
public class LlmProviderService extends JBoltBaseService<LlmProvider> {
	private final LlmProvider dao=new LlmProvider().dao();
	@Override
	protected LlmProvider dao() {
		return dao;
	}

	@Override
    protected int systemLogTargetType() {
        return ProjectSystemLogTargetType.NONE.getValue();
    }
		
	/**
	 * 后台管理数据查询
	 * @param pageNumber 第几页
	 * @param pageSize   每页几条数据
	 * @param keywords   关键词
	 * @param sortColumn  排序列名
	 * @param sortType  排序方式 asc desc
     * @param apiType API类型(openai/custom)
     * @param status 状态(0-禁用,1-启用)
	 * @return
	 */
	public Page<LlmProvider> getAdminDatas(int pageNumber, int pageSize, String keywords, String sortColumn, String sortType, String apiType, Boolean status) {
	    //创建sql对象
	    Sql sql = selectSql().page(pageNumber,pageSize);
	    //sql条件处理
        sql.eq("api_type",apiType);
        sql.eqBooleanToChar("status",status);
        //关键词模糊查询
        sql.likeMulti(keywords,"name", "remark");
        //排序
        sql.orderBy(sortColumn,sortType);
		return paginate(sql);
	}
	
	/**
	 * 保存
	 * @param llmProvider
	 * @return
	 */
	public Ret save(LlmProvider llmProvider) {
		if(llmProvider==null || isOk(llmProvider.getId())) {
			return fail(JBoltMsg.PARAM_ERROR);
		}
		boolean success=llmProvider.save();
		if(success) {
			//添加日志
			//addSaveSystemLog(llmProvider.getId(), JBoltUserKit.getUserId(), llmProvider.getName());
		}
		return ret(success);
	}
	
	/**
	 * 更新
	 * @param llmProvider
	 * @return
	 */
	public Ret update(LlmProvider llmProvider) {
		if(llmProvider==null || notOk(llmProvider.getId())) {
			return fail(JBoltMsg.PARAM_ERROR);
		}
		//更新时需要判断数据存在
		LlmProvider dbLlmProvider=findById(llmProvider.getId());
		if(dbLlmProvider==null) {return fail(JBoltMsg.DATA_NOT_EXIST);}
		boolean success=llmProvider.update();
		if(success) {
			//添加日志
			//addUpdateSystemLog(llmProvider.getId(), JBoltUserKit.getUserId(), llmProvider.getName());
		}
		return ret(success);
	}
	
	/**
	 * 删除数据后执行的回调
	 * @param llmProvider 要删除的model
	 * @param kv 携带额外参数一般用不上
	 * @return
	 */
	@Override
	protected String afterDelete(LlmProvider llmProvider, Kv kv) {
		//addDeleteSystemLog(llmProvider.getId(), JBoltUserKit.getUserId(),llmProvider.getName());
		return null;
	}
	
	/**
	 * 检测是否可以删除
	 * @param llmProvider model
	 * @param kv 携带额外参数一般用不上
	 * @return
	 */
	@Override
	public String checkInUse(LlmProvider llmProvider, Kv kv) {
		//这里用来覆盖 检测是否被其它表引用
		return null;
	}
	
	/**
	 * toggle操作执行后的回调处理
	 */
	@Override
	protected String afterToggleBoolean(LlmProvider llmProvider, String column, Kv kv) {
		//addUpdateSystemLog(llmProvider.getId(), JBoltUserKit.getUserId(), llmProvider.getName(),"的字段["+column+"]值:"+llmProvider.get(column));
		/**
		switch(column){
		    case "status":
		        break;
		}
		*/
		return null;
	}

	/**
	 * 获取启用的提供商列表
	 * @return
	 */
	public java.util.List<LlmProvider> getEnabledList() {
		return find("SELECT * FROM llm_provider WHERE status = 1 ORDER BY priority ASC");
	}

}