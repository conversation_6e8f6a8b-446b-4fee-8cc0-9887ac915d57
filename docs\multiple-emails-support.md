# 多邮箱地址支持功能

## 功能概述

`EmailMessagesService.getEmailHistory()` 方法现在支持传入多个邮箱地址，可以使用逗号或分号进行分隔。这个功能允许用户一次性查询多个邮箱的邮件往来历史。

## 支持的格式

### 1. 单个邮箱地址
```
<EMAIL>
```

### 2. 逗号分隔的多个邮箱
```
<EMAIL>,<EMAIL>,<EMAIL>
```

### 3. 分号分隔的多个邮箱
```
<EMAIL>;<EMAIL>;<EMAIL>
```

### 4. 混合分隔符
```
<EMAIL>,<EMAIL>;<EMAIL>
```

### 5. 包含显示名称的邮箱
```
张三 <<EMAIL>>,李四 <<EMAIL>>
```

### 6. 包含空格的邮箱（会自动处理）
```
 <EMAIL> , <EMAIL> ; <EMAIL> 
```

## 实现细节

### 核心方法

#### `parseMultipleEmails(String emailParam)`
- **功能**: 解析多个邮箱地址字符串
- **参数**: `emailParam` - 包含一个或多个邮箱地址的字符串
- **返回**: `String[]` - 解析后的邮箱地址数组
- **特性**:
  - 支持逗号 `,` 和分号 `;` 分隔符
  - 自动去除前后空格
  - 提取纯邮箱地址（去除显示名称）
  - 过滤空值和无效格式

#### `extractEmailAddress(String addressString)`
- **功能**: 从邮件地址字符串中提取纯邮箱地址
- **参数**: `addressString` - 可能包含显示名称的邮件地址
- **返回**: `String` - 纯邮箱地址
- **示例**:
  - 输入: `"张三 <<EMAIL>>"`
  - 输出: `"<EMAIL>"`

### 权限控制逻辑

多邮箱查询遵循与单邮箱相同的权限控制规则：

1. **用户管理的邮箱**: 如果查询的邮箱列表中包含用户管理的邮箱，则可以查看所有相关邮件
2. **外部邮箱**: 如果查询的邮箱都不是用户管理的，则只能查看与用户管理邮箱的来往邮件

### SQL 查询优化

对于多个邮箱的查询，系统会：
- 为每个邮箱生成相应的查询条件
- 使用 `OR` 连接多个邮箱的查询条件
- 保持与单邮箱查询相同的性能特征

## 使用示例

### 控制器调用
```java
// 前端传递多个邮箱参数
String email = "<EMAIL>,<EMAIL>;<EMAIL>";

// 构建查询参数
Kv params = Kv.by("email", email)
        .set("page", 1)
        .set("pageSize", 100);

// 调用服务方法
Ret result = emailMessagesService.getEmailHistory(1, 100, params);
```

### 前端调用
```javascript
// AJAX 请求示例
$.ajax({
    url: '/admin/emailMessages/getEmailHistory',
    type: 'POST',
    data: {
        email: '<EMAIL>,<EMAIL>;<EMAIL>',
        page: 1,
        pageSize: 100
    },
    success: function(result) {
        if (result.state === 'ok') {
            console.log('查询成功:', result.data);
        }
    }
});
```

## 错误处理

### 输入验证
- **空字符串**: 返回 "邮箱地址不能为空"
- **null值**: 返回 "邮箱地址不能为空"  
- **无效格式**: 返回 "邮箱地址格式不正确"
- **只有分隔符**: 返回 "邮箱地址格式不正确"

### 示例错误情况
```java
// 这些输入会被正确处理
Kv.by("email", "")           // -> "邮箱地址不能为空"
Kv.by("email", ",;,;")       // -> "邮箱地址格式不正确"
Kv.create()                  // -> "邮箱地址不能为空"
```

## 性能考虑

### 查询复杂度
- 多邮箱查询的 SQL 复杂度与邮箱数量成正比
- 建议单次查询的邮箱数量不超过 10 个
- 对于大量邮箱查询，建议分批处理

### 索引建议
为了优化多邮箱查询性能，建议创建以下索引：
```sql
-- 邮件消息表索引
CREATE INDEX idx_email_messages_from_address ON email_messages(from_address);
CREATE INDEX idx_email_messages_to_address ON email_messages(to_address);
CREATE INDEX idx_email_messages_cc_address ON email_messages(cc_address);

-- 用户邮箱关联表索引
CREATE INDEX idx_user_email_user_id_email ON user_email(user_id, email);
```

## 测试验证

### 测试文件
`src/test/java/cn/jbolt/admin/emailmessages/MultipleEmailsTest.java`

### 运行测试
```bash
mvn exec:java -Dexec.mainClass="cn.jbolt.admin.emailmessages.MultipleEmailsTest" -Dexec.classpathScope=test
```

### 测试覆盖
- ✅ 单个邮箱地址
- ✅ 逗号分隔多邮箱
- ✅ 分号分隔多邮箱
- ✅ 混合分隔符
- ✅ 显示名称格式
- ✅ 空格处理
- ✅ 错误格式验证

## 版本信息
- **实现日期**: 2025-08-05
- **版本**: 1.0.0
- **作者**: Augment Agent
- **兼容性**: 向后兼容，不影响现有单邮箱查询功能
