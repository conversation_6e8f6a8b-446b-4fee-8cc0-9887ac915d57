package cn.jbolt.admin.emailmessages;

import cn.jbolt._admin.permission.PermissionKey;
import cn.jbolt.admin.company.CompanyService;
import cn.jbolt.admin.emailaccount.EmailAccountService;
import cn.jbolt.admin.emails.EmailsService;
import cn.jbolt.admin.emailtracking.EmailTrackingService;
import cn.jbolt.common.config.JBoltUploadFolder;
import cn.jbolt.common.model.*;
import cn.jbolt.common.service.OpenAIService;
import cn.jbolt.core.controller.base.JBoltBaseController;
import cn.jbolt.core.kit.JBoltUserKit;
import cn.jbolt.core.model.JboltFile;
import cn.jbolt.core.permission.*;
import cn.jbolt.core.service.JBoltFileService;
import cn.jbolt.llm.service.LlmService;
import cn.jbolt.mail.gpt.client.EmailClient;
import cn.jbolt.mail.gpt.client.EmailClientPool;
import cn.jbolt.mail.gpt.fetch.plugin.EmailTranslationPlugin;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.core.Path;
import com.jfinal.kit.Kv;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;

import static cn.jbolt.common.util.FileKit.listEmailScreenshots;
import static cn.jbolt.common.util.StringKit.getDisplayName;

/**
 * 邮件消息管理
 *
 * @ClassName: EmailMessagesAdminController
 * @author: 总管理
 * @date: 2024-05-15 10:34
 */
@CheckPermission(PermissionKey.EMAIL_MESSAGES_ALL)
@UnCheckIfSystemAdmin
@Before(JBoltAdminAuthInterceptor.class)
@Path(value = "/admin/emailMessages", viewPath = "/_view/admin/emailmessages")
public class EmailMessagesAdminController extends JBoltBaseController {

    @Inject
    private EmailAccountService emailAccountService;

    @Inject
    private EmailsService emailsService;

    @Inject
    private CompanyService companyService;

    @Inject
    private EmailMessagesService service;

    @Inject
    private JBoltFileService jboltFileService;

    @Inject
    private OpenAIService openAIService;

    @Inject
    private EmailAttachmentFixService emailAttachmentFixService;

    @Inject
    private EmailTrackingService emailTrackingService;

    /**
     * 邮件处理中心主页
     */
    public void index() {
        render("index.html");
    }

    /**
     * 获取邮件跟踪信息
     */
    public void getEmailTracking() {
        String messageId = getPara("messageId");
        Long emailMessageId = getParaToLong("emailMessageId");

        if (cn.hutool.core.util.StrUtil.isBlank(messageId) && emailMessageId == null) {
            renderJson(Ret.fail("参数不能为空"));
            return;
        }

        try {
            cn.jbolt.common.model.EmailTrackingRecord trackingRecord = null;

            // 优先使用messageId查找
            if (cn.hutool.core.util.StrUtil.isNotBlank(messageId)) {
                trackingRecord = emailTrackingService.findByMessageId(messageId);
            }

            if (trackingRecord == null) {
                renderJson(Ret.ok("未找到跟踪记录").set("data", null));
                return;
            }

            // 获取打开事件
            java.util.List<cn.jbolt.common.model.EmailTrackingOpen> opens =
                    emailTrackingService.getOpenEvents(trackingRecord.getTrackingId(), 50);

            java.util.Map<String, Object> data = new java.util.HashMap<>();
            data.put("record", trackingRecord);
            data.put("opens", opens);

            renderJson(Ret.ok("获取跟踪信息成功").set("data", data));

        } catch (Exception e) {
            com.jfinal.kit.LogKit.error("获取邮件跟踪信息失败", e);
            renderJson(Ret.fail("获取跟踪信息失败: " + e.getMessage()));
        }
    }

    /**
     * 获取邮箱账户树结构
     */
    public void getEmailAccountTree() {
        renderJson(emailAccountService.getEmailAccountTree(JBoltUserKit.getUserId()));
    }

    /**
     * 获取公司/客户树结构
     */
    public void getCompanyTree() {
        renderJson(companyService.getCompanyClientTree(JBoltUserKit.getUserId()));
    }

    /**
     * 获取邮件列表
     */
    public void listEmails() {
        Kv params = Kv.create();

        // 获取查询参数
        String emailAccountId = getPara("emailAccountId");
        String accountType = getPara("accountType");
        String companyId = getPara("companyId");
        String companyType = getPara("companyType");
        String email = getPara("email");
        String subject = getPara("subject");
        String content = getPara("content");

        int pageNumber = getParaToInt("page", 1);
        int pageSize = getParaToInt("pageSize", 500);

        // 构建查询参数
        if (emailAccountId != null && !emailAccountId.isEmpty()) {
            params.set("accountId", emailAccountId);
        }
        if (accountType != null && !accountType.isEmpty()) {
            params.set("accountType", accountType);
        }
        if (companyId != null && !companyId.isEmpty()) {
            params.set("companyId", companyId);
        }
        if (companyType != null && !companyType.isEmpty()) {
            params.set("companyType", companyType);
        }
        if (email != null && !email.isEmpty()) {
            params.set("email", email);
        }
        if (subject != null && !subject.isEmpty()) {
            params.set("subject", subject);
        }
        if (content != null && !content.isEmpty()) {
            params.set("content", content);
        }

        // 查询邮件列表
        Page<?> page = service.paginateEmails(pageNumber, pageSize, params);
        renderJsonData(page);
    }

    /**
     * 删除邮件
     */
    public void delete() {
        Long id = getParaToLong("id");
        if (id == null) {
            renderJsonFail("邮件ID不能为空");
            return;
        }

        boolean success = service.deleteEmail(id);
        if (success) {
            renderJsonSuccess("删除成功");
        } else {
            renderJsonFail("删除失败");
        }
    }

    /**
     * 删除邮件（从查看页面调用）
     */
    public void deleteEmail() {
        Long emailId = getLong("emailId");
        if (emailId == null) {
            renderJsonFail("邮件ID不能为空");
            return;
        }

        try {
            // 检查邮件是否存在
            EmailMessages email = service.findById(emailId);
            if (email == null) {
                renderJsonFail("邮件不存在");
                return;
            }

            // 执行删除操作
            boolean success = service.deleteEmail(emailId);
            if (success) {
                renderJsonSuccess("邮件删除成功");
            } else {
                renderJsonFail("删除失败");
            }
        } catch (Exception e) {
            LogKit.error("删除邮件失败", e);
            renderJsonFail("删除失败：" + e.getMessage());
        }
    }

    /**
     * 邮件重发
     */
    public void resendEmail() {
        Long emailId = getLong("emailId");
        if (emailId == null) {
            renderJsonFail("邮件ID不能为空");
            return;
        }

        try {
            // 获取原邮件信息
            EmailMessages originalEmail = service.findById(emailId);
            if (originalEmail == null) {
                renderJsonFail("原邮件不存在");
                return;
            }

            // 检查邮件是否是发送的邮件（只有发送的邮件才能重发）
            if (!Boolean.TRUE.equals(originalEmail.getIsSent())) {
                renderJsonFail("只能重发已发送的邮件");
                return;
            }

            // 获取发件邮箱账号
            String fromEmail = originalEmail.getFromAddress();
            EmailAccount emailAccount = emailAccountService.findByEmail(fromEmail);
            if (emailAccount == null) {
                renderJsonFail("发件邮箱账号不存在");
                return;
            }

            // 准备重发邮件的数据
            String toEmail = originalEmail.getToAddress();
            String ccEmail = originalEmail.getCcAddress();
            String subject = originalEmail.getSubject();
            String originalContent = originalEmail.getContentHtml() != null ?
                    originalEmail.getContentHtml() : originalEmail.getContentText();

            // 获取原邮件的附件文件
            List<File> attachmentFiles = getOriginalEmailAttachments(originalEmail.getId());

            // 重新发送邮件 - EmailsService会自动处理内容中的图片CID引用
            Ret result = emailsService.sendEmail(fromEmail, toEmail, ccEmail, subject,
                    originalContent, null, attachmentFiles);

            if (result.isOk()) {
                renderJsonSuccess("邮件重发成功");
            } else {
                renderJsonFail("重发失败：" + result.getStr("msg"));
            }

        } catch (Exception e) {
            LogKit.error("邮件重发失败", e);
            renderJsonFail("重发失败：" + e.getMessage());
        }
    }

    /**
     * 获取原邮件的附件文件列表
     */
    private List<File> getOriginalEmailAttachments(Long emailId) {
        List<File> attachmentFiles = new ArrayList<>();

        try {
            // 获取原邮件的所有附件（只获取普通附件，不包括内联图片）
            List<EmailAttachments> attachments = new EmailAttachments().dao().find(
                    "SELECT * FROM email_attachments WHERE email_id = ? AND status = 2 AND (cid IS NULL OR cid = '')",
                    emailId);

            // 将附件路径转换为File对象
            for (EmailAttachments attachment : attachments) {
                String filePath = attachment.getPath();
                if (filePath != null && !filePath.isEmpty()) {
                    File file = new File(filePath);
                    if (file.exists()) {
                        attachmentFiles.add(file);
                        LogKit.info("添加重发附件: {}", file.getName());
                    } else {
                        LogKit.warn("附件文件不存在: {}", filePath);
                    }
                }
            }

        } catch (Exception e) {
            LogKit.error("获取原邮件附件失败", e);
        }

        return attachmentFiles;
    }

    /**
     * 跳转到回复邮件页面
     */
    public void reply() {
        Long id = getParaToLong("id");
        if (id == null) {
            renderError(404);
            return;
        }

        setAttr("emailId", id);
        setAttr("type", "reply");
        render("compose.html");
    }

    /**
     * 跳转到转发邮件页面
     */
    public void forward() {
        Long id = getParaToLong("id");
        if (id == null) {
            renderError(404);
            return;
        }

        setAttr("emailId", id);
        setAttr("type", "forward");
        render("compose.html");
    }

    /**
     * 获取用户的邮箱账户列表
     */
    public void getEmailAccounts() {
        renderJsonData(emailAccountService.getEmailAccountsByUserId(JBoltUserKit.getUserId()));
    }

    /**
     * 获取邮件基本信息
     */
    public void getEmailInfo() {
        Long emailId = getLong("emailId");
        if (emailId == null) {
            renderJsonFail("邮件ID不能为空");
            return;
        }

        EmailMessages email = new EmailMessages().dao().findById(emailId);
        if (email == null) {
            renderJsonFail("邮件不存在");
            return;
        }

        // 构建邮件基本信息
        Map<String, Object> emailInfo = new HashMap<>();
        emailInfo.put("id", email.getId());
        emailInfo.put("subject", email.getSubject());
        emailInfo.put("contentHtml", email.getContentHtml());
        emailInfo.put("contentText", email.getContentText());
        emailInfo.put("fromAddress", email.getFromAddress());
        emailInfo.put("toAddress", email.getToAddress());
        emailInfo.put("sentDate", email.getSentDate());

        renderJsonData(emailInfo);
    }

    /**
     * 翻译邮件内容
     */
    public void translateEmail() {
        Long emailId = getLong("emailId");
        String content = getPara("content");
        boolean forceTranslate = getParaToBoolean("forceTranslate", false);
        boolean translateImages = getParaToBoolean("translateImages", true); // 默认启用图片翻译

        // 如果有邮件ID，优先使用邮件ID进行翻译
        if (isOk(emailId)) {
            // 如果不是强制翻译，先查找是否已有翻译记录
            if (!forceTranslate) {
                EmailTranslation translation = new EmailTranslation().dao().findFirst(
                        "SELECT * FROM email_translation WHERE email_id = ?", emailId);
                if (translation != null && translation.getTranslatedContent() != null) {
                    // 已有翻译记录，直接返回完整的翻译对象
                    renderJsonData(translation);
                    return;
                }
            }

            // 没有翻译记录或需要强制重新翻译，调用翻译插件
            EmailTranslationPlugin emailTranslationPlugin = new EmailTranslationPlugin();
            String translationContent = emailTranslationPlugin.translateEmail(emailId, translateImages);
            if (translationContent != null) {
                // 获取最新的翻译记录并返回完整对象
                EmailTranslation translation = new EmailTranslation().dao().findFirst(
                        "SELECT * FROM email_translation WHERE email_id = ?", emailId);
                if (translation != null) {
                    // 构建包含分离翻译结果的响应数据
                    Map<String, Object> responseData = new HashMap<>();
                    responseData.put("id", translation.getId());
                    responseData.put("emailId", translation.getEmailId());
                    responseData.put("originalContent", translation.getOriginalContent());
                    responseData.put("translatedContent", translation.getTranslatedContent());
                    responseData.put("screenshotPath", translation.getScreenshotPath());
                    responseData.put("createTime", translation.getCreateTime());
                    responseData.put("updateTime", translation.getUpdateTime());

                    // 分离的翻译内容
                    responseData.put("subjectOriginal", translation.getSubjectOriginal());
                    responseData.put("subjectTranslated", translation.getSubjectTranslated());
                    responseData.put("contentOriginal", translation.getContentOriginal());
                    responseData.put("contentTranslated", translation.getContentTranslated());
                    responseData.put("imageTranslations", translation.getImageTranslationResults());

                    renderJsonData(responseData);
                } else {
                    // 如果没有找到记录（可能是翻译成功但未保存到数据库），构造一个临时对象
                    Map<String, Object> tempData = new HashMap<>();
                    tempData.put("emailId", emailId);
                    tempData.put("translatedContent", translationContent);
                    renderJsonData(tempData);
                }
            } else {
                renderJsonFail("翻译失败");
            }
        }
        // 如果没有邮件ID但有内容，直接调用LLM进行翻译
        else if (isOk(content)) {
            // 使用LlmService进行翻译
            String translationContent = LlmService.me().callLlm("硅基流动", "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B",
                    "请将以下内容翻译成中文（如果已是中文则翻译成英文）：\n\n" + content);
            if (translationContent != null) {
                // 构造一个临时的翻译对象返回
                EmailTranslation tempTranslation = new EmailTranslation()
                        .setOriginalContent(content)
                        .setTranslatedContent(translationContent)
                        .setCreateTime(new Date());
                renderJsonData(tempTranslation);
            } else {
                renderJsonFail("翻译失败");
            }
        }
        // 既没有邮件ID也没有内容，返回错误
        else {
            renderJsonFail("邮件ID和内容不能同时为空");
        }
    }

    /**
     * 获取邮件翻译记录
     */
    public void getEmailTranslation() {
        Long emailId = getLong("emailId");
        if (notOk(emailId)) {
            renderJsonFail("邮件ID不能为空");
            return;
        }

        // 查找翻译记录
        EmailTranslation translation = new EmailTranslation().dao().findFirst(
                "SELECT * FROM email_translation WHERE email_id = ?", emailId);

        if (translation != null && translation.getTranslatedContent() != null) {
            // 已有翻译记录
            renderJsonData(translation);
        } else {
            // 没有翻译记录
            renderJsonFail("未找到翻译记录");
        }
    }

    /**
     * 提取邮件要点
     */
    public void extractKeyPoints() {
        String content = get("content");
        if (notOk(content)) {
            renderJsonFail("内容不能为空");
            return;
        }
        String keyPoints = LlmService.me().callLlm("硅基流动", "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B",
                "请总结以下邮件的关键信息和要点：\n\n" + content);
        if (keyPoints != null) {
            renderJsonData(keyPoints);
        } else {
            renderJsonFail("提取要点失败");
        }
    }

    /**
     * 生成回复内容
     */
    public void generateReply() {
        String content = get("content");
        if (notOk(content)) {
            renderJsonFail("内容不能为空");
            return;
        }
        String reply = LlmService.me().callLlm("硅基流动", "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B",
                "请根据以下邮件内容生成一个专业、得体的回复：\n\n" + content);
        if (reply != null) {
            renderJsonData(reply);
        } else {
            renderJsonFail("生成回复失败");
        }
    }

    /**
     * 切换邮件已读/未读状态
     */
    public void toggleEmailReadStatus() {
        String id = get("id");
        if (notOk(id)) {
            renderJsonFail("参数错误");
            return;
        }
        renderJson(emailsService.toggleReadStatus(id));
    }

    /**
     * 上传图片（富文本编辑器使用）
     */
    public void uploadImage() {
        try {
            UploadFile file = getFile("file", JBoltUploadFolder.todayFolder(JBoltUploadFolder.EMAIL_IMAGE));
            if (notImage(file)) {
                // 返回TinyMCE期望的错误格式
                getResponse().setStatus(400);
                renderJson(Kv.by("error", Kv.by("message", "请上传图片类型文件")));
                return;
            }

            Ret saveImageFile = jboltFileService.saveImageFile(file, JBoltUploadFolder.todayFolder(JBoltUploadFolder.EMAIL_IMAGE));
            if (saveImageFile.isFail()) {
                // 返回TinyMCE期望的错误格式
                getResponse().setStatus(500);
                String msg = saveImageFile.getStr("msg");
                if (StringUtils.isEmpty(msg)) {
                    msg = "图片上传失败";
                }
                renderJson(Kv.by("error", Kv.by("message", msg)));
                return;
            }

            // 返回TinyMCE期望的成功格式
            Kv imageLocation = Kv.by("location", saveImageFile.getStr("data"));
            renderJson(imageLocation);
        } catch (Exception e) {
            LogKit.error("图片上传异常", e);
            // 返回TinyMCE期望的错误格式
            getResponse().setStatus(500);
            renderJson(Kv.by("error", Kv.by("message", "图片上传失败：" + e.getMessage())));
        }
    }

    /**
     * 即时上传附件
     */
    public void uploadAttachment() {
        // 获取是否保留原文件名的参数
        boolean keepOriginalFileName = getBoolean("keepOriginalFileName", true); // 默认保留原文件名

        UploadFile file = getFile("file", JBoltUploadFolder.todayFolder(JBoltUploadFolder.EMAIL_ATTACHMENT));
        if (file == null) {
            renderJsonFail("请选择文件");
            return;
        }

        // 检查文件大小（30MB限制）
        if (file.getFile().length() > 30 * 1024 * 1024) {
            renderJsonFail("文件大小不能超过30MB");
            return;
        }

        try {
            JboltFile jboltFile;
            if (keepOriginalFileName) {
                // 保留原文件名
                jboltFile = jboltFileService.saveJBoltFileWithOriginalName(file,
                        JBoltUploadFolder.todayFolder(JBoltUploadFolder.EMAIL_ATTACHMENT),
                        JboltFile.FILE_TYPE_ATTACHMENT);
            } else {
                // 使用系统生成的文件名
                jboltFile = jboltFileService.saveJBoltFile(file,
                        JBoltUploadFolder.todayFolder(JBoltUploadFolder.EMAIL_ATTACHMENT),
                        JboltFile.FILE_TYPE_ATTACHMENT);
            }

            if (jboltFile != null) {
                // 返回文件信息
                Kv result = Kv.by("id", jboltFile.getId())
                        .set("fileName", jboltFile.getFileName())
                        .set("originalFileName", file.getOriginalFileName())
                        .set("fileSize", file.getFile().length())
                        .set("localPath", jboltFile.getLocalPath())
                        .set("localUrl", jboltFile.getLocalUrl())
                        .set("keepOriginalFileName", keepOriginalFileName);
                renderJsonData(result);
            } else {
                renderJsonFail("文件上传失败");
            }
        } catch (Exception e) {
            LogKit.error("附件上传失败", e);
            renderJsonFail("附件上传失败：" + e.getMessage());
        }
    }

    /**
     * AI写作
     */
    public void aiWrite() {
        String prompt = get("prompt");
        if (notOk(prompt)) {
            renderJsonFail("提示语不能为空");
            return;
        }
        String content = get("content", ""); // content 可以为空
        String fullPrompt = prompt + (isOk(content) ? "\n\n参考内容：\n" + content : "");
        String result = LlmService.me().callLlm("硅基流动", "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B", fullPrompt);
        if (result != null) {
            renderJsonData(result);
        } else {
            renderJsonFail("AI写作失败");
        }
    }

    /**
     * AI续写
     */
    public void aiContinueWrite() {
        String content = get("content");
        String prompt = get("prompt", ""); // prompt 可以为空
        if (notOk(content)) {
            renderJsonFail("内容不能为空");
            return;
        }
        String fullPrompt = (isOk(prompt) ? prompt + "\n\n" : "请基于以下内容继续写下去，保持语气和风格的一致性：\n\n") + content;
        String result = LlmService.me().callLlm("硅基流动", "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B", fullPrompt);
        if (result != null) {
            renderJsonData(result);
        } else {
            renderJsonFail("AI续写失败");
        }
    }

    /**
     * AI改写
     */
    public void aiRewrite() {
        String content = get("content");
        String prompt = get("prompt", ""); // prompt 可以为空
        if (notOk(content)) {
            renderJsonFail("内容不能为空");
            return;
        }
        String fullPrompt = (isOk(prompt) ? prompt + "\n\n" : "请对以下内容进行改写，保持原意的同时使表达更加专业、得体：\n\n") + content;
        String result = LlmService.me().callLlm("硅基流动", "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B", fullPrompt);
        if (result != null) {
            renderJsonData(result);
        } else {
            renderJsonFail("AI改写失败");
        }
    }

    /**
     * 保存草稿
     */
    public void saveDraft() {
        // 由于移除了enctype="multipart/form-data"，现在可以直接获取参数
        String fromEmail = get("fromEmail");
        String toEmail = get("toEmail");
        String ccEmail = get("ccEmail");
        String subject = get("subject");
        String content = get("content");
        String originalEmailId = get("originalEmailId");
        String draftId = get("draftId");
        String[] uploadedAttachmentIds = getParaValues("uploadedAttachmentIds"); // 已上传的附件ID列表

        // 使用新的保存草稿方法，支持已上传的附件ID
        renderJson(emailsService.saveDraft(fromEmail, toEmail, ccEmail, subject, content, null, originalEmailId,
                draftId, uploadedAttachmentIds));
    }

    /**
     * 将所有邮件标记为已读
     */
    public void markAllAsRead() {
        String companyId = get("companyId");
        renderJson(emailsService.markAllAsRead(companyId));
    }

    /**
     * 重新接收邮件
     */
    public void reloadEmail() {
        String emailId = get("id");
        if (notOk(emailId)) {
            renderJsonFail("邮件ID不能为空");
            return;
        }

        try {
            // 获取本地邮件信息
            EmailMessages email = emailsService.findById(emailId);
            if (email == null) {
                renderJsonFail("邮件不存在");
                return;
            }

            // 获取邮件账号信息
            EmailAccount account = emailAccountService.findByEmail(email.getEmailAccount());
            if (account == null) {
                renderJsonFail("邮件账号不存在");
                return;
            }
            EmailClientPool clientPool = EmailClientPool.getInstance();
            EmailClient client = clientPool.getClient(account, EmailClient.Mode.COMPLETE_MODE);
            client.fetchEmailByEmailMessages(email);
        } catch (Exception e) {
            LogKit.error("重新接收邮件失败", e);
            renderJsonFail("重新接收失败：" + e.getMessage());
        }
    }

    public void aiProcess() {
        String action = getPara("action");
        String option = getPara("option");
        String content = getPara("content");
        String prompt = getPara("prompt");
        String originalEmailId = getPara("originalEmailId");
        String llmProvider = getPara("llmProvider", "硅基流动");
        String llmModel = getPara("llmModel", "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B");

        try {
            // 构建完整的提示词
            StringBuilder fullPrompt = new StringBuilder();

            // 根据action构建基础提示词
            switch (action) {
                case "write":
                    fullPrompt.append("请根据以下信息写一封专业的邮件：\n");
                    break;
                case "reply":
                    fullPrompt.append("请根据以下邮件内容生成一个专业、得体的回复：\n");
                    break;
                case "rewrite":
                    fullPrompt.append("请改写以下邮件内容，使其更加专业、清晰、得体：\n");
                    break;
                case "continue":
                    fullPrompt.append("请基于以下内容继续写下去，保持语气和风格的一致性：\n");
                    break;
                case "translate":
                    fullPrompt.append("请将以下内容翻译成中文（如果已是中文则翻译成英文）：\n");
                    break;
                case "summarize":
                    fullPrompt.append("请总结以下邮件的关键信息和要点：\n");
                    break;
                default:
                    fullPrompt.append("请处理以下邮件内容：\n");
            }

            // 添加用户自定义提示词
            if (isOk(prompt)) {
                fullPrompt.append("\n用户要求：").append(prompt).append("\n");
            }

            // 获取原邮件上下文信息
            if (isOk(originalEmailId)) {
                EmailMessages originalEmail = service.findById(originalEmailId);
                if (originalEmail != null) {
                    // 构建原邮件上下文
                    fullPrompt.append("\n原邮件信息：\n");
                    fullPrompt.append("发件人: ").append(originalEmail.getFromAddress()).append("\n");
                    fullPrompt.append("收件人: ").append(originalEmail.getToAddress()).append("\n");
                    if (isOk(originalEmail.getCcAddress())) {
                        fullPrompt.append("抄送: ").append(originalEmail.getCcAddress()).append("\n");
                    }
                    fullPrompt.append("主题: ").append(originalEmail.getSubject()).append("\n");
                    fullPrompt.append("发送时间: ").append(originalEmail.getSentDate()).append("\n");
                    fullPrompt.append("内容: ").append(originalEmail.getContent()).append("\n");

                    // 获取附件信息
                    if (originalEmail.getHasAttachments() != null && originalEmail.getHasAttachments()) {
                        Ret attachmentsRet = emailsService.getAttachments(originalEmailId);
                        if (attachmentsRet.isOk()) {
                            List<Map<String, Object>> attachments = attachmentsRet.getAs("data");
                            if (attachments != null && !attachments.isEmpty()) {
                                fullPrompt.append("\n原邮件包含以下附件：\n");
                                for (Map<String, Object> attachment : attachments) {
                                    String fileName = (String) attachment.get("fileName");
                                    String fileSize = (String) attachment.get("size");
                                    fullPrompt.append("- ").append(fileName);
                                    if (fileSize != null) {
                                        fullPrompt.append(" (").append(fileSize).append(")");
                                    }
                                    fullPrompt.append("\n");
                                }
                                fullPrompt.append("请在回复中适当提及这些附件。\n");
                            }
                        }
                    }
                }
            }

            // 添加格式要求
            fullPrompt.append("\n请确保回复内容：\n");
            fullPrompt.append("1. 语言专业、得体\n");
            fullPrompt.append("2. 结构清晰、逻辑合理\n");
            fullPrompt.append("3. 符合商务邮件规范\n");
            fullPrompt.append("4. 直接返回邮件正文内容，无需包含称呼和签名\n\n");

            // 添加要处理的内容
            if (isOk(content)) {
                fullPrompt.append("要处理的内容：\n").append(content);
            }

            // 使用LlmService调用AI
            String result = LlmService.me().callLlm(llmProvider, llmModel, fullPrompt.toString());

            renderJson(Ret.ok("data", result));
        } catch (Exception e) {
            LogKit.error("AI处理失败", e);
            renderJson(Ret.fail("AI处理失败: " + e.getMessage()));
        }
    }

    /**
     * 获取可用的LLM提供商和模型
     */
    public void getLlmProviders() {
        try {
            // 查询启用的LLM提供商
            List<Record> providers = Db.find(
                    "SELECT p.id, p.name, p.description, " +
                            "       GROUP_CONCAT(CONCAT(m.model_identifier, '|', m.model_name) SEPARATOR ';') as models " +
                            "FROM llm_provider p " +
                            "LEFT JOIN llm_model m ON p.id = m.provider_id AND m.status = 1 " +
                            "WHERE p.status = 1 " +
                            "GROUP BY p.id, p.name, p.description " +
                            "ORDER BY p.priority"
            );

            // 处理模型数据
            for (Record provider : providers) {
                String modelsStr = provider.getStr("models");
                if (isOk(modelsStr)) {
                    String[] modelPairs = modelsStr.split(";");
                    List<Map<String, String>> modelList = new ArrayList<>();
                    for (String pair : modelPairs) {
                        String[] parts = pair.split("\\|");
                        if (parts.length == 2) {
                            Map<String, String> model = new HashMap<>();
                            model.put("identifier", parts[0]);
                            model.put("name", parts[1]);
                            modelList.add(model);
                        }
                    }
                    provider.set("modelList", modelList);
                }
                provider.remove("models");
            }

            renderJsonData(providers);
        } catch (Exception e) {
            LogKit.error("获取LLM提供商失败", e);
            renderJsonFail("获取LLM提供商失败：" + e.getMessage());
        }
    }

    public void getMail() {
        EmailMessages emails = emailsService.findById(get("id"));
        renderJsonData(emails);
    }

    public void getUrl() {
        String email = get("email");
        if (StringUtils.isEmpty(email)) {
            renderJsonFail("没有匹配的邮箱");
            return;
        }

        EmailAccount emailAccount = emailAccountService.findByEmail(email);
        if (emailAccount != null) {
            String emails = JBoltUserKit.getEmails();
            if (StringUtils.isEmpty(emails)) {
                renderJsonFail("邮箱邮件获取失败!");
                return;
            }

            // 将 emails 按逗号分割并转换为 Set 以便快速查找
            Set<String> emailSet = new HashSet<>(Arrays.asList(emails.split(",")));

            // 检查 emailSet 中是否包含 emailAccount.getId() 的字符串形式
            if (emailSet.contains(String.valueOf(emailAccount.getId()))) {
                renderJsonData(Ret.ok("这个邮箱是用户邮箱!").set("url", "/admin/emailAccount/message/0/" + email));
                return;
            }

            renderJsonFail("邮箱邮件获取失败!");
            return;
        }

        Integer companyId = companyService.findIdFromEmail(email);
        if (companyId != null) {
            renderJsonData(Ret.ok("这个邮箱是客户公司邮箱!").set("url", "/admin/company/messages?email=" + email));
        }

    }

    /**
     * 查看邮件详情页面
     */
    public void viewEmail() {
        Long id = getLong(0);
        LogKit.info("Viewing email with ID: {}", id);
        try {
            EmailMessages email = service.findById(id);
            if (email == null) {
                LogKit.warn("Email not found with ID: {}", id);
                renderError(404);
                return;
            }
            LogKit.info("Found email: {}", email);

            // 检查是否需要跳转到翻译页面
            Long userId = JBoltUserKit.getUserId();
            if (userId != null) {
                boolean isOnlyFollowUpRole = checkUserOnlyHasFollowUpRole(userId);
                boolean isCustomerEmail = checkIsCustomerEmail(String.valueOf(id));

                // 如果用户只有跟单角色且邮件是客户发来的，跳转到翻译页面
                if (isOnlyFollowUpRole && isCustomerEmail) {
                    LogKit.info("User has only follow-up role and email is from customer, redirecting to translation page");
                    redirect("/admin/emailMessages/showTranslation?emailId=" + id);
                    return;
                }
            }

            // 设置邮件已读
            if (!email.getIsRead()) {
                LogKit.info("Marking email as read: {}", id);
                email.setIsRead(true);
                email.update();
            }

            setAttr("email", email);
            render("viewEmail.html");

        } catch (Exception e) {
            LogKit.error("Error viewing email: {}", id, e);
            renderError(500);
        }
    }

    /**
     * 获取邮箱文件夹列表
     */
    public void getFolders() {
        Long id = getLong("id");
        renderJson(emailsService.getFolderList(id));
    }

    /**
     * 获取指定文件夹的邮件列表
     */
    public void getEmailsByFolder() {
        Long accountId = getLong("accountId");
        String folderName = get("folderName");
        String keywords = get("keywords");
        renderJsonData(
                emailsService.getEmailsByFolder(accountId, folderName, keywords, getPageNumber(), getPageSize()));
    }

    /**
     * 获取邮件详情
     */
    public void getEmailDetail() {
        String emailId = get("emailId");
        renderJson(emailsService.getEmailDetail(emailId));
    }

    public void getAttachments() {
        String emailId = get("emailId");
        if (notOk(emailId)) {
            renderJsonFail("邮件ID不能为空");
            return;
        }
        Ret attachments = emailsService.getAttachments(emailId);
        renderJsonData(attachments);
    }

    /**
     * 下载附件
     */
    @UnLogin
    public void downloadAttachment() {
        String emailId = get("emailId");
        // 兼容直接预览的URL格式
        if (StrKit.isBlank(emailId)) {
            emailId = get("id");
        }
        String fileName = get("fileName");

        if (StrKit.isBlank(emailId) || StrKit.isBlank(fileName)) {
            renderJson(Ret.fail("邮件ID和文件名不能为空"));
            return;
        }

        Ret ret = emailsService.downloadAttachment(emailId, fileName);
        if (ret.isFail()) {
            renderJson(ret);
            return;
        }

        File file = ret.getAs("file");
        String downloadFileName = ret.getStr("fileName");

        try {
            // 检查是否是图片文件，如果是图片且是预览请求，则直接显示
            boolean isPreview = getParaToBoolean("preview", false);
            if (isPreview && downloadFileName != null &&
                    downloadFileName.toLowerCase().matches(".*\\.(jpg|jpeg|png|gif|bmp|webp)$")) {
                renderFile(file);
            } else {
                renderFile(file, downloadFileName);
            }
        } finally {
            // 确保文件流关闭
            if (file != null && file.exists()) {
                try {
                    FileInputStream fis = new FileInputStream(file);
                    fis.close();
                } catch (IOException e) {
                    LogKit.error("关闭文件流异常", e);
                }
            }
        }
    }

    /**
     * 检查附件是否存在
     */
    public void checkAttachmentExists() {
        String emailId = get("emailId");
        String fileName = get("fileName");

        if (StrKit.isBlank(emailId) || StrKit.isBlank(fileName)) {
            renderJson(Ret.fail("邮件ID和文件名不能为空"));
            return;
        }

        Ret ret = emailsService.checkAttachmentExists(emailId, fileName);
        renderJson(ret);
    }

    /**
     * 单独翻译功能
     */
    public void translateSingle() {
        Long emailId = getLong("emailId");
        String type = getPara("type");
        String originalContent = getPara("originalContent");
        Integer promptId = getInt("promptId");
        String customPrompt = getPara("customPrompt");
        Long providerId = getLong("providerId");
        Long modelId = getLong("modelId");

        if (emailId == null) {
            renderJsonFail("邮件ID不能为空");
            return;
        }

        if (StrKit.isBlank(type)) {
            renderJsonFail("翻译类型不能为空");
            return;
        }

        if (providerId == null || modelId == null) {
            renderJsonFail("请选择AI提供商和模型");
            return;
        }

        try {
            // 调用翻译服务
            EmailTranslationPlugin emailTranslationPlugin = new EmailTranslationPlugin();
            String result = emailTranslationPlugin.translateSingle(emailId, type, originalContent,
                    promptId, customPrompt, providerId, modelId);

            if (result != null) {
                renderJson(Ret.ok("msg", "翻译成功").set("data", result));
            } else {
                renderJsonFail("翻译失败");
            }
        } catch (Exception e) {
            LOG.error("单独翻译失败", e);
            renderJsonFail("翻译过程中发生错误：" + e.getMessage());
        }
    }

    /**
     * 保存编辑的翻译内容
     */
    public void saveEditedTranslation() {
        Long emailId = getLong("emailId");
        String type = getPara("type");
        String translatedContent = getPara("translatedContent");
        String imageTranslations = getPara("imageTranslations");

        if (emailId == null) {
            renderJsonFail("邮件ID不能为空");
            return;
        }

        if (StrKit.isBlank(type)) {
            renderJsonFail("翻译类型不能为空");
            return;
        }

        try {
            EmailTranslation translation = new EmailTranslation().dao().findFirst(
                    "SELECT * FROM email_translation WHERE email_id = ?", emailId);

            if (translation == null) {
                renderJsonFail("翻译记录不存在");
                return;
            }

            // 根据类型更新对应字段
            switch (type) {
                case "subject":
                    translation.setSubjectTranslated(translatedContent);
                    break;
                case "content":
                    translation.setContentTranslated(translatedContent);
                    break;
                case "images":
                    translation.setImagesTranslation(imageTranslations);
                    break;
                default:
                    renderJsonFail("未知的翻译类型");
                    return;
            }

            translation.updateTimestamp();
            boolean success = translation.update();

            if (success) {
                renderJson(Ret.ok("msg", "保存成功"));
            } else {
                renderJsonFail("保存失败");
            }
        } catch (Exception e) {
            LOG.error("保存编辑翻译失败", e);
            renderJsonFail("保存过程中发生错误：" + e.getMessage());
        }
    }

    public void downloadAllAttachments() {
        String emailId = get("emailId");

        Ret ret = emailsService.downloadAllAttachments(emailId);
        if (ret.isFail()) {
            renderJson(ret);
            return;
        }

        File file = ret.getAs("file");
        String fileName = ret.getStr("fileName");

        try {
            renderFile(file, fileName);

            // 异步删除临时文件，给文件下载预留足够时间
            if (file != null && file.exists()) {
                // 使用线程池延迟删除临时文件
                java.util.concurrent.Executors.newSingleThreadScheduledExecutor().schedule(() -> {
                    try {
                        if (file.exists()) {
                            Files.delete(file.toPath());
                            LogKit.info("临时附件包已删除: " + file.getAbsolutePath());
                        }
                    } catch (IOException e) {
                        LogKit.error("删除临时文件失败: " + file.getAbsolutePath(), e);
                    }
                }, 120, java.util.concurrent.TimeUnit.SECONDS); // 120秒后删除临时文件
            }
        } catch (Exception e) {
            LogKit.error("下载附件包失败", e);
            renderJson(Ret.fail("下载失败"));

            // 如果下载失败，立即删除临时文件
            if (file != null && file.exists()) {
                try {
                    Files.delete(file.toPath());
                } catch (IOException ioException) {
                    LogKit.error("删除临时文件失败", ioException);
                }
            }
        }
    }

    /**
     * 回复邮件页面
     */
    public void replyEmail() {
        // 支持两种方式获取邮件ID：路径参数和查询参数
        String emailId = getPara();
        if (notOk(emailId)) {
            emailId = getPara("originalEmailId");
        }

        if (notOk(emailId)) {
            renderError(404);
            return;
        }

        EmailMessages emailMessages = emailsService.findById(emailId);
        if (emailMessages == null) {
            renderError(404);
            return;
        }

        // 检查是否是回复全部模式
        String replyAll = getPara("replyAll");
        boolean isReplyAll = "true".equals(replyAll);

        if (isReplyAll) {
            // 回复全部模式：设置抄送人为原邮件的抄送人
            String originalCcAddress = emailMessages.getCcAddress();
            if (StrKit.notBlank(originalCcAddress)) {
                set("ccEmail", originalCcAddress);
            }
            set("isReplyAll", true);
        }

        set("originalEmail", emailMessages);
        render("replyEmail.html");
    }

    /**
     * 转发邮件页面
     */
    public void forwardEmail() {
        String emailId = getPara();
        if (notOk(emailId)) {
            renderError(404);
            return;
        }

        Ret ret = emailsService.getEmailDetail(emailId);
        if (ret.isFail()) {
            renderError(404);
            return;
        }

        // 获取翻译内容（如果有）
        String translationContent = getPara("translationContent");
        if (StrKit.notBlank(translationContent)) {
            set("translationContent", translationContent);
        }

        // 获取原始邮件的附件
        Ret attachmentsRet = emailsService.getAttachments(emailId);
        if (attachmentsRet.isOk()) {
            List<Map<String, Object>> attachments = attachmentsRet.getAs("data");
            if (attachments != null && !attachments.isEmpty()) {
                // 处理附件，添加预览信息
                for (Map<String, Object> attachment : attachments) {
                    String fileName = (String) attachment.get("fileName");
                    // 判断是否为图片文件
                    boolean isImage = fileName != null && fileName.toLowerCase().matches(".*\\.(jpg|jpeg|png|gif|bmp|webp)$");
                    attachment.put("isImage", isImage);

                    // 添加下载URL
                    String url = getRequest().getContextPath() + "/admin/emailMessages/downloadAttachment?id=" + emailId + "&fileName=" + fileName;
                    attachment.put("url", url);
                }
                set("originalAttachments", attachments);
            }
        }

        // 获取转发的收件人邮箱（基于发件人是否为客户）
        String forwardToEmails = getForwardToEmailsByClient(emailId);
        if (StrKit.notBlank(forwardToEmails)) {
            set("forwardToEmails", forwardToEmails);
        }

        set("originalEmail", ret.get("data"));
        set("isForward", true);
        render("replyEmail.html");
    }

    /**
     * 根据邮件发件人获取转发收件人邮箱
     * 逻辑：邮件id → from_address → client表查询 → company_client获取公司id → user_company获取用户 → 用户邮箱
     * 使用一次性JOIN查询优化性能，避免N+1查询问题
     *
     * @param emailId 邮件ID
     * @return 收件人邮箱地址列表，多个邮箱用分号分隔
     */
    private String getForwardToEmailsByClient(String emailId) {
        try {
            // 1. 根据邮件ID获取发件人邮箱
            EmailMessages email = service.findById(emailId);
            if (email == null || StrKit.isBlank(email.getFromAddress())) {
                return null;
            }

            String fromAddress = email.getFromAddress();
            LogKit.info("转发邮件 - 发件人邮箱: {}", fromAddress);

            // 2. 使用一次性JOIN查询获取所有相关用户邮箱，避免N+1查询问题
            String sql = "SELECT DISTINCT u.email, c.name as client_name, comp.name as company_name, u.name as user_name " +
                    "FROM client c " +
                    "INNER JOIN company_client cc ON c.id = cc.client_id " +
                    "INNER JOIN company comp ON cc.company_id = comp.id " +
                    "INNER JOIN user_company uc ON comp.id = uc.company_id " +
                    "INNER JOIN jb_user u ON uc.user_id = u.id " +
                    "WHERE c.email = ? " +
                    "  AND u.email IS NOT NULL " +
                    "  AND u.email != ''";

            List<Record> userRecords = Db.find(sql, fromAddress);

            if (userRecords.isEmpty()) {
                LogKit.info("转发邮件 - 发件人 {} 不是客户或没有关联的用户邮箱", fromAddress);
                return null;
            }

            // 3. 提取邮箱地址并去重
            Set<String> userEmails = new HashSet<>();
            String clientName = null;

            for (Record record : userRecords) {
                String userEmail = record.getStr("email");
                if (StrKit.notBlank(userEmail)) {
                    userEmails.add(userEmail);
                    if (clientName == null) {
                        clientName = record.getStr("client_name");
                    }
                    LogKit.info("转发邮件 - 找到用户邮箱: {} (用户: {}, 公司: {})",
                            userEmail, record.getStr("user_name"), record.getStr("company_name"));
                }
            }

            // 4. 将收件人邮箱用分号连接
            if (!userEmails.isEmpty()) {
                String result = String.join(";", userEmails);
                LogKit.info("转发邮件 - 客户 {} 自动设置收件人: {} (共{}个邮箱)",
                        clientName, result, userEmails.size());
                return result;
            }

            LogKit.info("转发邮件 - 客户 {} 未找到任何关联用户的邮箱", clientName);
            return null;

        } catch (Exception e) {
            LogKit.error("获取转发收件人邮箱失败", e);
            return null;
        }
    }

    /**
     * 新建邮件页面
     */
    public void composeEmail() {
        // 检查是否有草稿ID参数
        String draftId = getPara("draftId");
        String toEmail = get("toEmail", "");

        if (isOk(draftId)) {
            // 如果有草稿ID，加载草稿信息
            EmailMessages draft = emailsService.findById(draftId);
            if (draft != null && draft.getIsDraft() != null && draft.getIsDraft()) {
                // 设置草稿信息到页面
                set("draft", draft);
                set("fromEmail", draft.getFromAddress());
                set("toEmail", draft.getToAddress());
                set("ccEmail", draft.getCcAddress());
                set("subject", draft.getSubject());
                set("content", draft.getContentHtml());
                set("draftId", draftId);
                set("isLoadingDraft", true);

                // 设置页面标题
                set("pageTitle", "编辑草稿 - " + (draft.getSubject() != null ? draft.getSubject() : "无主题"));
            } else {
                // 草稿不存在或无效
                set("toEmail", toEmail);
                set("draftError", "指定的草稿不存在或已失效");
                set("pageTitle", "撰写新邮件");
            }
        } else {
            // 正常的新建邮件
            set("toEmail", toEmail);
            set("pageTitle", "撰写新邮件");
        }

        render("replyEmail.html");
    }

    /**
     * 显示翻译页面
     */
    public void showTranslation() {
        Long emailId = getLong("emailId");
        if (emailId == null) {
            renderJson(Ret.fail("emailId不能为空"));
            return;
        }
        String content = getPara("content");

        if (StringUtils.isEmpty(content)) {
            EmailMessages emailMessages = emailsService.findById(emailId);
            if (emailMessages != null) {
                content = emailMessages.getContent();
            }
        }

        // 检查是否是Ajax请求，如果是，可能是弹窗调用
        boolean isAjax = isAjaxRequest();

        set("content", content);
        set("emailId", emailId);

        // 如果是Ajax请求，渲染不带布局的页面
        if (isAjax) {
            render("translation_content.html");
        } else {
            render("translation.html");
        }
    }

    /**
     * 判断是否是Ajax请求
     */
    private boolean isAjaxRequest() {
        String header = getRequest().getHeader("X-Requested-With");
        return header != null && "XMLHttpRequest".equalsIgnoreCase(header);
    }

    /**
     * 获取邮件完整内容
     * 当邮件的fetch_status为0时，需要获取完整内容
     */
    public void fetchCompleteContent() {
        Long emailId = getLong("emailId");
        if (emailId == null) {
            renderJson(Ret.fail("emailId不能为空"));
            return;
        }

        try {
            // 获取邮件信息
            EmailMessages email = emailsService.findById(emailId);
            if (email == null) {
                renderJson(Ret.fail("邮件不存在"));
                return;
            }
            EmailAccount account = emailAccountService.findById(email.getAccountId());

            EmailClientPool clientPool = EmailClientPool.getInstance();
            EmailClient client = clientPool.getClient(account, EmailClient.Mode.COMPLETE_MODE);
            boolean success = client.fetchEmailByEmailMessages(email);

            if (success) {
                // 重新加载邮件信息
                email = emailsService.findById(emailId);
                renderJson(Ret.ok("msg", "获取邮件完整内容成功").set("data", email));
            } else {
                renderJson(Ret.fail("获取邮件完整内容失败"));
            }
        } catch (Exception e) {
            LogKit.error("获取邮件完整内容失败", e);
            renderJson(Ret.fail("获取邮件完整内容失败: " + e.getMessage()));
        }
    }

    public void getEmailAlias() {
        String email = getPara("email");
        if (StringUtils.isBlank(email)) {
            renderFail("邮箱地址不能为空");
            return;
        }

        Record alias = Db.findFirst("SELECT * FROM email_alias WHERE email=?", email);
        renderJsonData(alias);
    }

    /**
     * 根据邮箱地址查找相关的公司信息
     */
    public void getCompaniesFromEmails() {
        String fromAddress = getPara("fromAddress");
        String toAddress = getPara("toAddress");
        String ccAddress = getPara("ccAddress");

        if (StringUtils.isBlank(fromAddress) && StringUtils.isBlank(toAddress) && StringUtils.isBlank(ccAddress)) {
            renderJsonFail("至少需要提供一个邮箱地址");
            return;
        }

        try {
            List<Record> companies = service.getCompaniesFromEmails(fromAddress, toAddress, ccAddress);
            renderJsonData(companies);
        } catch (Exception e) {
            LogKit.error("查找邮箱相关公司信息失败", e);
            renderJsonFail("查找失败：" + e.getMessage());
        }
    }

    /**
     * 获取邮件附件，用于回复邮件时添加原始邮件的附件
     */
    public void getEmailAttachments() {
        String emailId = get("emailId");
        if (notOk(emailId)) {
            renderJsonFail("邮件ID不能为空");
            return;
        }
        Ret attachments = emailsService.getAttachments(emailId);
        renderJsonData(attachments);
    }

    /**
     * 统一判断邮件操作按钮的跳转逻辑
     * 如果用户只有跟单角色且邮件是客户发来的，则跳转到翻译页面
     */
    public void getEmailActionUrl() {
        String emailId = get("emailId");
        String action = get("action"); // reply, translate, delete等

        if (notOk(emailId) || notOk(action)) {
            renderJsonFail("参数不能为空");
            return;
        }

        try {
            // 获取当前用户ID
            Long userId = JBoltUserKit.getUserId();
            if (userId == null) {
                renderJsonFail("用户未登录");
                return;
            }

            // 判断用户是否只有跟单角色
            boolean isOnlyFollowUpRole = checkUserOnlyHasFollowUpRole(userId);

            // 获取邮件信息并判断是否是客户发来的邮件
            boolean isCustomerEmail = checkIsCustomerEmail(emailId);

            // 如果用户只有跟单角色且邮件是客户发来的，统一跳转到翻译页面
            if (isOnlyFollowUpRole && isCustomerEmail) {
                renderJsonData(Ret.ok("跳转到翻译页面").set("url", "/admin/emailMessages/showTranslation?emailId=" + emailId + "&_jb_rqtype_=dialog"));
            } else {
                // 否则返回原有的跳转逻辑
                String url = getOriginalActionUrl(emailId, action);
                renderJsonData(Ret.ok("执行原有逻辑").set("url", url));
            }

        } catch (Exception e) {
            LogKit.error("判断邮件操作跳转逻辑失败", e);
            renderJsonFail("操作失败：" + e.getMessage());
        }
    }

    /**
     * 检查用户是否只有跟单角色
     */
    private boolean checkUserOnlyHasFollowUpRole(Long userId) {
        try {
            // 获取用户的角色列表
            String userRoleIds = JBoltUserKit.getUserRoleIds();
            if (StrKit.isBlank(userRoleIds)) {
                return false;
            }

            // 将角色ID字符串转换为数组
            String[] roleIdArray = userRoleIds.split(",");

            // 如果用户有多个角色，则不是只有跟单角色
            if (roleIdArray.length != 1) {
                return false;
            }

            // 获取唯一的角色ID
            Long roleId = Long.parseLong(roleIdArray[0].trim());

            // 查询角色信息，判断是否是跟单角色
            // 这里需要根据实际的跟单角色名称或SN来判断
            // 假设跟单角色的名称包含"跟单"或者有特定的SN
            Record roleRecord = Db.findFirst("SELECT * FROM jb_role WHERE id = ?", roleId);
            if (roleRecord != null) {
                String roleName = roleRecord.getStr("name");
                String roleSn = roleRecord.getStr("sn");

                // 判断是否是跟单角色（可以根据实际情况调整判断条件）
                return (roleName != null && roleName.contains("跟单")) ||
                        (roleSn != null && roleSn.toLowerCase().contains("follow"));
            }

            return false;
        } catch (Exception e) {
            LogKit.error("检查用户角色失败", e);
            return false;
        }
    }

    /**
     * 检查邮件是否是客户发来的
     */
    private boolean checkIsCustomerEmail(String emailId) {
        try {
            EmailMessages email = service.findById(emailId);
            if (email == null) {
                return false;
            }

            String fromAddress = email.getFromAddress();
            String toAddress = email.getToAddress();
            String emailAccount = email.getEmailAccount();

            // 使用与EmailMessagesService中相同的逻辑判断邮件方向
            // 首先判断是否是客户相关邮件
            boolean isCustomerRelated = service.isClientEmail(fromAddress) || service.isClientEmail(toAddress);

            if (isCustomerRelated) {
                boolean isFromClient = service.isClientEmail(fromAddress);
                boolean isToClient = service.isClientEmail(toAddress);

                // 如果from是客户，to不是客户，说明是客户发来的邮件
                return isFromClient && !isToClient;
            }

            return false;
        } catch (Exception e) {
            LogKit.error("检查邮件是否是客户发来的失败", e);
            return false;
        }
    }

    /**
     * 获取原有的操作URL
     */
    private String getOriginalActionUrl(String emailId, String action) {
        switch (action.toLowerCase()) {
            case "reply":
                return "/admin/emailMessages/replyEmail/" + emailId + "?_jb_rqtype_=dialog";
            case "translate":
                return "/admin/emailMessages/showTranslation?emailId=" + emailId + "&_jb_rqtype_=dialog";
            case "view":
                return "/admin/emailMessages/viewEmail/" + emailId + "?_jb_rqtype_=dialog";
            case "delete":
                return ""; // 删除操作不需要URL，直接执行AJAX
            default:
                return "";
        }
    }

    /**
     * 测试页面 - 邮件操作逻辑测试
     */
    public void testActionLogic() {
        render("test_action_logic.html");
    }

    public void saveEmailAlias() {
        String id = getPara("id");
        String email = getPara("email");
        String label = getPara("label");
        String remark = getPara("remark");

        if (StringUtils.isBlank(email)) {
            renderFail("邮箱地址不能为空");
            return;
        }

        boolean success = false;
        Record alias = null;

        if (StringUtils.isNotBlank(id)) {
            // 更新
            alias = Db.findFirst("SELECT * FROM email_alias WHERE id=?", id);
            if (alias != null) {
                alias.set("label", label);
                alias.set("remark", remark);
                success = Db.update("email_alias", alias);
            }
        } else {
            // 先查询是否已存在
            alias = Db.findFirst("SELECT * FROM email_alias WHERE email=?", email);
            if (alias != null) {
                alias.set("label", label);
                alias.set("remark", remark);
                success = Db.update("email_alias", alias);
            } else {
                // 新增
                alias = new Record();
                alias.set("email", email);
                alias.set("label", label);
                alias.set("remark", remark);
                success = Db.save("email_alias", alias);
            }
        }

        if (success) {
            renderJson(Ret.ok("msg", "保存成功").set("data", alias));
        } else {
            renderJson(Ret.fail("保存失败"));
        }
    }

    /**
     * 获取邮件往来历史
     */
    public void getEmailHistory() {
        try {
            String email = getPara("email");
            String contactName = getPara("contactName");
            String type = getPara("type");
            String typeFilter = getPara("typeFilter");
            String readStatusFilter = getPara("readStatusFilter");
            String subjectSearch = getPara("subjectSearch");
            Boolean customerOnly = getBoolean("customerOnly", false);

            int page = getParaToInt("page", 1);
            int pageSize = getParaToInt("pageSize", 100);
            String sortField = getPara("sortField", "sent_date");
            String sortDirection = getPara("sortDirection", "desc");

            if (StrKit.isBlank(email)) {
                renderJsonFail("邮箱地址不能为空");
                return;
            }

            // 构建查询参数
            Kv params = Kv.by("email", email)
                    .set("contactName", contactName)
                    .set("type", type)
                    .set("typeFilter", typeFilter)
                    .set("readStatusFilter", readStatusFilter)
                    .set("subjectSearch", subjectSearch)
                    .set("customerOnly", customerOnly)
                    .set("sortField", sortField)
                    .set("sortDirection", sortDirection);

            // 调用 service 获取历史邮件
            Ret result = service.getEmailHistory(page, pageSize, params);
            renderJson(result);

        } catch (Exception e) {
            LogKit.error("获取邮件历史失败", e);
            renderJsonFail("获取邮件历史失败：" + e.getMessage());
        }
    }

    /**
     * 历史邮件页面
     */
    public void history() {
        String email = getPara("email");
        String contactName = getPara("contactName");
        String type = getPara("type");
        Boolean customerOnly = getBoolean("customerOnly", false);

        if (StrKit.isBlank(email)) {
            renderError(404);
            return;
        }
        if (StringUtils.isEmpty(contactName)) {
            contactName = getDisplayName(email);
        }
        setAttr("email", email);
        setAttr("contactName", contactName);
        setAttr("type", type);
        setAttr("customerOnly", customerOnly);
        setAttr("pageTitle", "邮件往来历史 - " + email);

        render("history.html");
    }

    /**
     * 获取某个邮箱账户的所有邮件
     */
    public void getEmailsByAccount() {
        try {
            String emailAccount = getPara("emailAccount");
            String folderName = getPara("folderName");
            String typeFilter = getPara("typeFilter");
            String readStatusFilter = getPara("readStatusFilter");
            String subjectSearch = getPara("subjectSearch");

            int page = getParaToInt("page", 1);
            int pageSize = getParaToInt("pageSize", 100);
            String sortField = getPara("sortField", "sent_date");
            String sortDirection = getPara("sortDirection", "desc");

            if (StrKit.isBlank(emailAccount)) {
                renderJsonFail("邮箱账户不能为空");
                return;
            }

            // 构建查询参数
            Kv params = Kv.by("emailAccount", emailAccount)
                    .set("folderName", folderName)
                    .set("typeFilter", typeFilter)
                    .set("readStatusFilter", readStatusFilter)
                    .set("subjectSearch", subjectSearch)
                    .set("sortField", sortField)
                    .set("sortDirection", sortDirection);

            // 调用 service 获取邮箱邮件
            Ret result = service.getEmailsByAccount(page, pageSize, params);
            renderJson(result);

        } catch (Exception e) {
            LogKit.error("获取邮箱邮件失败", e);
            renderJsonFail("获取邮箱邮件失败：" + e.getMessage());
        }
    }

    /**
     * 邮箱邮件页面
     */
    public void accountEmails() {
        String emailAccount = getPara("emailAccount");
        String accountName = getPara("accountName");

        if (StrKit.isBlank(emailAccount)) {
            renderError(404);
            return;
        }
        if (StringUtils.isEmpty(accountName)) {
            accountName = getDisplayName(emailAccount);
        }
        setAttr("emailAccount", emailAccount);
        setAttr("accountName", accountName);
        setAttr("pageTitle", "邮箱邮件 - " + emailAccount);

        render("account_emails.html");
    }

    /**
     * 获取某个公司或osclub客户的所有邮件
     */
    public void getEmailsByCompany() {
        try {
            Integer osclubId = getParaToInt("osclubId");
            Integer companyId = getParaToInt("companyId");
            String clientEmail = getPara("clientEmail");
            String typeFilter = getPara("typeFilter");
            String readStatusFilter = getPara("readStatusFilter");
            String subjectSearch = getPara("subjectSearch");

            int page = getParaToInt("page", 1);
            int pageSize = getParaToInt("pageSize", 100);
            String sortField = getPara("sortField", "sent_date");
            String sortDirection = getPara("sortDirection", "desc");

            if (companyId == null) {
                if (osclubId != null) {
                    Company company = companyService.findFirstByOsclubId(osclubId);
                    if (company != null) {
                        companyId = company.getId();
                    } else {
                        renderJsonFail("公司或osclubID不能为空");
                        return;
                    }
                } else {
                    renderJsonFail("公司或osclubID不能为空");
                    return;
                }
            }

            // 构建查询参数
            Kv params = Kv.by("companyId", companyId)
                    .set("clientEmail", clientEmail)
                    .set("typeFilter", typeFilter)
                    .set("readStatusFilter", readStatusFilter)
                    .set("subjectSearch", subjectSearch)
                    .set("sortField", sortField)
                    .set("sortDirection", sortDirection);

            // 调用 service 获取公司邮件
            Ret result = service.getEmailsByCompany(page, pageSize, params);
            renderJson(result);

        } catch (Exception e) {
            LogKit.error("获取公司邮件失败", e);
            renderJsonFail("获取公司邮件失败：" + e.getMessage());
        }
    }

    /**
     * 公司邮件页面
     */
    public void companyEmails() {
        Integer companyId = getParaToInt("companyId");
        Integer osclubId = getParaToInt("osclubId");
        String companyName = getPara("companyName");

        if (companyId == null) {
            if (osclubId != null) {
                Company company = companyService.findFirstByOsclubId(osclubId);
                if (company != null) {
                    companyId = company.getId();
                    companyName = company.getNickName();
                } else {
                    renderError(404);
                    return;
                }
            } else {
                renderError(404);
                return;
            }
        }

        // 如果没有提供公司名称，从数据库获取
        if (StringUtils.isEmpty(companyName)) {
            companyName = companyService.findById(companyId).getNickName();
        }

        setAttr("companyId", companyId);
        setAttr("companyName", companyName);
        setAttr("pageTitle", "公司邮件 - " + companyName);

        render("company_emails.html");
    }

    /**
     * 获取用户管理的邮箱账号列表
     */
    public void getUserEmailAccounts() {
        try {
            Long userId = JBoltUserKit.getUserId();
            String searchTerm = getPara("q", "").trim();

            StringBuilder sql = new StringBuilder();
            sql.append("SELECT email_name, email_account_id, email FROM user_email ");
            sql.append("WHERE user_id = ? AND email_account_id IS NOT NULL ");

            List<Object> params = new ArrayList<>();
            params.add(userId);

            if (StrKit.notBlank(searchTerm)) {
                sql.append("AND (email_name LIKE ? OR email LIKE ?) ");
                String searchPattern = "%" + searchTerm + "%";
                params.add(searchPattern);
                params.add(searchPattern);
            }

            sql.append("ORDER BY email_name LIMIT 100");

            List<Record> accounts = Db.find(sql.toString(), params.toArray());

            renderJsonData(accounts);

        } catch (Exception e) {
            LogKit.error("获取用户邮箱账号列表失败", e);
            renderJsonFail("获取邮箱账号列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户管理的公司列表
     */
    public void getUserCompanies() {
        try {
            Long userId = JBoltUserKit.getUserId();
            String searchTerm = getPara("q", "").trim();

            StringBuilder sql = new StringBuilder();
            sql.append("SELECT nick_name, id FROM company ");
            sql.append("WHERE id IN (SELECT company_id FROM user_company WHERE account_id = ? and enable=1) ");

            List<Object> params = new ArrayList<>();
            params.add(userId);

            if (StrKit.notBlank(searchTerm)) {
                sql.append("AND nick_name LIKE ? ");
                params.add("%" + searchTerm + "%");
            }

            sql.append("ORDER BY nick_name LIMIT 100");

            List<Record> companies = Db.find(sql.toString(), params.toArray());

            renderJsonData(companies);

        } catch (Exception e) {
            LogKit.error("获取用户公司列表失败", e);
            renderJsonFail("获取公司列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取指定邮箱账户的文件夹列表
     */
    public void getEmailAccountFolders() {
        try {
            String emailAccount = getPara("emailAccount");
            if (StrKit.isBlank(emailAccount)) {
                renderJsonFail("邮箱账户不能为空");
                return;
            }

            // 首先通过email_account获取account_id
            Integer accountId = Db.queryInt("SELECT DISTINCT account_id FROM email_messages WHERE email_account = ? LIMIT 1", emailAccount);

            if (accountId == null) {
                // 如果没有找到account_id，直接按email_account查询
                List<Record> folders = Db.find("SELECT DISTINCT folder_name FROM email_messages WHERE email_account = ? ORDER BY folder_name", emailAccount);
                renderJsonData(folders);
                return;
            }

            // 使用account_id查询文件夹
            List<Record> folders = Db.find("SELECT DISTINCT folder_name FROM email_messages WHERE account_id = ? ORDER BY folder_name", accountId);

            renderJsonData(folders);

        } catch (Exception e) {
            LogKit.error("获取邮箱文件夹列表失败", e);
            renderJsonFail("获取文件夹列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取指定公司的客户列表
     */
    public void getCompanyClients() {
        try {
            Integer companyId = getParaToInt("companyId");
            if (companyId == null) {
                renderJsonFail("公司ID不能为空");
                return;
            }

            List<Record> clients = Db.find("SELECT * FROM client WHERE id IN (SELECT client_id FROM company_client WHERE company_id = ?) ORDER BY nickname", companyId);

            renderJsonData(clients);

        } catch (Exception e) {
            LogKit.error("获取公司客户列表失败", e);
            renderJsonFail("获取客户列表失败：" + e.getMessage());
        }
    }

    /**
     * AI排版邮件内容
     */
    public void aiTypesetting() {
        try {
            Long emailId = getParaToLong("emailId");
            if (emailId == null) {
                renderJsonFail("邮件ID不能为空");
                return;
            }

            // 获取邮件内容
            EmailMessages emailMessages = service.findById(emailId);
            if (emailMessages == null) {
                renderJsonFail("邮件不存在");
                return;
            }

            String originalContent = emailMessages.getContent();
            if (StrKit.isBlank(originalContent)) {
                renderJsonFail("邮件内容为空，无法进行AI排版");
                return;
            }

            // 调用AI排版服务
            String typesettedContent = service.aiTypesetting(originalContent, emailMessages.getSubject());

            if (StrKit.isBlank(typesettedContent)) {
                renderJsonFail("AI排版失败，请稍后重试");
                return;
            }

            if (typesettedContent.startsWith("{\"error\":{\"")) {
                renderJsonFail("AI排版失败，请稍后重试");
                return;
            }
            // 更新数据库中的邮件内容
            emailMessages.setContentHtml(typesettedContent);
            if (emailMessages.update()) {
                renderJsonData(typesettedContent);
            } else {
                renderJsonFail("更新邮件内容失败");
            }

        } catch (Exception e) {
            LogKit.error("AI排版失败", e);
            renderJsonFail("AI排版失败：" + e.getMessage());
        }
    }

    /**
     * 检查邮件AI排版状态
     */
    public void checkAITypesetStatus() {
        try {
            Long emailId = getParaToLong("emailId");
            if (emailId == null) {
                renderJsonFail("邮件ID不能为空");
                return;
            }
            EmailMessages emailMessages = service.findById(emailId);
            Boolean aiTypeset = emailMessages.getAiTypeset();
            renderJsonData(aiTypeset != null ? aiTypeset : false);

        } catch (Exception e) {
            LogKit.error("检查AI排版状态失败", e);
            renderJsonFail("检查AI排版状态失败：" + e.getMessage());
        }
    }

    /**
     * 邮件详情页面
     */
    public void detail() {
        Long emailId = getLong(0);
        if (emailId == null) {
            renderError(404);
            return;
        }

        EmailMessages email = service.findById(emailId);
        if (email == null) {
            renderError(404);
            return;
        }

        // 标记为已读
        if (!email.getIsRead()) {
            email.setIsRead(true);
            email.update();
        }

        setAttr("email", email);
        setAttr("pageTitle", "邮件详情 - " + (email.getSubject() != null ? email.getSubject() : "(无主题)"));

        render("detail.html");
    }

    /**
     * 获取邮件的图片附件
     */
    public void getEmailImageAttachments() {
        String emailId = getPara("emailId");
        if (StrKit.isBlank(emailId)) {
            renderJsonFail("邮件ID不能为空");
            return;
        }

        try {
            Ret result = emailsService.getEmailImageAttachments(emailId);
            renderJson(result);
        } catch (Exception e) {
            LogKit.error("获取邮件图片附件失败", e);
            renderJsonFail("获取邮件图片附件失败：" + e.getMessage());
        }
    }

    public void sendEmail() {
        // 添加调试日志 - 打印所有参数
        System.out.println("=== 发送邮件参数调试 ===");
        System.out.println("所有参数名: " + getParaMap().keySet());
        for (String key : getParaMap().keySet()) {
            System.out.println(key + " = " + get(key));
        }
        System.out.println("========================");

        // 由于移除了enctype="multipart/form-data"，现在可以直接获取参数
        String fromEmail = get("fromEmail");
        String toEmail = get("toEmail");
        String ccEmail = get("ccEmail", "");
        String subject = get("subject", "");
        String content = get("content", "");
        String originalEmailId = get("originalEmailId", "");
        boolean isForward = getBoolean("isForward", false);
        String[] forwardAttachments = getParaValues("forwardAttachments");
        String uploadedAttachmentIdsParam = get("uploadedAttachmentIds", ""); // 已上传的附件ID参数
        String[] uploadedAttachmentIds = null;

        // 处理附件ID参数，支持逗号分隔的字符串
        if (StrKit.notBlank(uploadedAttachmentIdsParam)) {
            uploadedAttachmentIds = uploadedAttachmentIdsParam.split(",");
            // 过滤空值并去除空格
            uploadedAttachmentIds = Arrays.stream(uploadedAttachmentIds)
                    .map(String::trim)
                    .filter(StrKit::notBlank)
                    .toArray(String[]::new);
        }

        // 添加调试日志
        System.out.println("=== 解析后的参数 ===");
        System.out.println("fromEmail: " + fromEmail);
        System.out.println("toEmail: " + toEmail);
        System.out.println("ccEmail: " + ccEmail);
        System.out.println("subject: " + subject);
        System.out.println("content: " + (content != null ? content.substring(0, Math.min(100, content.length())) + "..." : "null"));
        System.out.println("originalEmailId: " + originalEmailId);
        System.out.println("isForward: " + isForward);
        System.out.println("uploadedAttachmentIdsParam: " + uploadedAttachmentIdsParam);
        System.out.println("uploadedAttachmentIds: " + (uploadedAttachmentIds != null ? String.join(",", uploadedAttachmentIds) : "null"));
        System.out.println("uploadedAttachmentIds length: " + (uploadedAttachmentIds != null ? uploadedAttachmentIds.length : 0));
        System.out.println("========================");

        if (notOk(fromEmail) || notOk(toEmail)) {
            renderFail("发件人或收件人不能为空");
            return;
        }

        // 处理转发的附件
        List<File> forwardFiles = new ArrayList<>();
        if (isForward && forwardAttachments != null) {
            Ret attachmentsRet = emailsService.getAttachments(originalEmailId);
            List<Map<String, Object>> attachments = attachmentsRet.getAs("data");
            Map<String, Map<String, Object>> attachmentsMap = new HashMap<>();
            for (Map<String, Object> attachment : attachments) {
                attachmentsMap.put((String) attachment.get("fileName"), attachment);
            }
            for (String fileName : forwardAttachments) {
                if (StrKit.notBlank(fileName) && attachmentsMap.containsKey(fileName)) {
                    File attachFile = new File((String) attachmentsMap.get(fileName).get("path"));
                    forwardFiles.add(attachFile);
                }
            }
        }

        // 使用支持已上传附件的sendEmail方法，传入null作为files参数避免重复
        renderJson(emailsService.sendEmail(fromEmail, toEmail, ccEmail, subject, content, null, forwardFiles, uploadedAttachmentIds));
    }

    public void followUp() {
        String id = get("id");
        renderJson(emailsService.followUp(id));
    }

    public void important() {
        String id = get("id");
        renderJson(emailsService.important(id));
    }

    /**
     * 获取用户的邮件过滤预设列表
     */
    @UnCheck
    public void getFilterPresets() {
        Long userId = JBoltUserKit.getUserId();
        renderJsonData(service.getFilterPresets(userId));
    }

    /**
     * 保存邮件过滤预设
     */
    @UnCheck
    public void saveFilterPreset() {
        Long userId = JBoltUserKit.getUserId();
        String name = getPara("name");
        String filters = getPara("filters");
        String logic = getPara("logic", "or");
        Boolean isDefault = getParaToBoolean("isDefault", false);

        if (StrKit.isBlank(name)) {
            renderFail("预设名称不能为空");
            return;
        }

        if (StrKit.isBlank(filters)) {
            renderFail("过滤条件不能为空");
            return;
        }

        // 优先使用支持默认预设的方法
        if (isDefault != null) {
            renderJson(service.saveFilterPreset(userId, name, filters, logic, isDefault));
        } else {
            renderJson(service.saveFilterPreset(userId, name, filters, logic));
        }
    }

    /**
     * 删除邮件过滤预设
     */
    @UnCheck
    public void deleteFilterPreset() {
        Long userId = JBoltUserKit.getUserId();
        Long presetId = getLong("id");

        if (presetId == null) {
            renderFail("预设ID不能为空");
            return;
        }

        renderJson(service.deleteFilterPreset(userId, presetId));
    }

    /**
     * 应用邮件过滤预设
     */
    @UnCheck
    public void applyFilterPreset() {
        Long userId = JBoltUserKit.getUserId();
        Long presetId = getLong("id");

        if (presetId == null) {
            renderFail("预设ID不能为空");
            return;
        }

        renderJsonData(service.getFilterPreset(userId, presetId));
    }

    /**
     * 切换预设的默认状态
     */
    @UnCheck
    public void toggleDefaultPreset() {
        Long userId = JBoltUserKit.getUserId();
        Long presetId = getLong("id");

        if (presetId == null) {
            renderFail("预设ID不能为空");
            return;
        }

        renderJson(service.toggleDefaultPreset(userId, presetId));
    }

    /**
     * 获取邮箱地址列表，用于自动完成功能
     * 包括email_account和client表中的邮箱
     */
    @UnCheck
    public void getEmailAddresses() {
        List<Record> emailAddresses = new ArrayList<>();

        // 获取email_account表中的邮箱
        List<EmailAccount> accounts = emailAccountService.findAllEnabled();
        for (EmailAccount account : accounts) {
            Record record = new Record();
            record.set("value", account.getUsername());
            record.set("label", (account.getNickname() != null ? account.getNickname() + " " : "") + "<" + account.getUsername() + ">");
            record.set("category", "邮箱账户");
            emailAddresses.add(record);
        }

        // 获取client表中的邮箱
        List<Record> clients = Db.find("SELECT distinct u.email, c.nick_name, u.email_name FROM user_email u left join company c on u.company_id=c.id where u.user_id=? and u.type=1", JBoltUserKit.getUserId());
        for (Record client : clients) {
            String email = client.getStr("email");
            if (email != null && !email.isEmpty()) {
                // 处理可能有多个邮箱的情况（以逗号或分号分隔）
                String[] emails = email.split("[,;]");
                for (String singleEmail : emails) {
                    singleEmail = singleEmail.trim();
                    if (!singleEmail.isEmpty()) {
                        Record record = new Record();
                        record.set("value", singleEmail);
                        String companyName = client.getStr("nick_name");
                        String displayName = (client.getStr("email_name") != null ? client.getStr("email_name") : "") +
                                (companyName != null ? " (" + companyName + ")" : "");
                        record.set("label", displayName + " <" + singleEmail + ">");
                        record.set("category", "客户");
                        emailAddresses.add(record);
                    }
                }
            }
        }

        renderJsonData(emailAddresses);
    }

    /**
     * 获取签名列表
     */
    public void getSignatures() {
        String fromEmail = getPara("fromEmail");

        // 构建查询条件
        StringBuilder sql = new StringBuilder("SELECT * FROM email_signature WHERE enable = 1");
        List<Object> params = new ArrayList<>();

        sql.append(" AND (create_user_id = ? OR is_global = 1)");
        params.add(JBoltUserKit.getUserId());

        // 按排序顺序获取
        sql.append(" ORDER BY sort_rank ASC, id ASC");

        // 执行查询
        List<Record> signatures = Db.find(sql.toString(), params.toArray());

        renderJsonData(signatures);
    }

    /**
     * 获取签名内容
     */
    public void getSignatureContent() {
        Long id = getParaToLong("id");
        if (id == null) {
            renderJsonFail("签名ID不能为空");
            return;
        }

        // 查询签名
        Record signature = Db.findById("email_signature", id);
        if (signature == null) {
            renderJsonFail("签名不存在");
            return;
        }

        renderJsonData(signature);
    }

    /**
     * 获取模板列表
     */
    public void getTemplates() {
        // 构建查询条件
        StringBuilder sql = new StringBuilder("SELECT * FROM email_template WHERE enable = 1");
        List<Object> params = new ArrayList<>();

        // 获取当前用户的模板和全局模板
        sql.append(" AND (create_user_id = ? OR is_global = 1)");
        params.add(JBoltUserKit.getUserId());

        // 按排序顺序获取
        sql.append(" ORDER BY sort_rank ASC, id ASC");

        // 执行查询
        List<Record> templates = Db.find(sql.toString(), params.toArray());

        renderJsonData(templates);
    }

    /**
     * 获取模板内容
     */
    public void getTemplateContent() {
        Long id = getParaToLong("id");
        if (id == null) {
            renderJsonFail("模板ID不能为空");
            return;
        }

        // 查询模板
        Record template = Db.findById("email_template", id);
        if (template == null) {
            renderJsonFail("模板不存在");
            return;
        }

        renderJsonData(template);
    }

    /**
     * 获取同一目录下的所有截图文件
     */
    public void getAllScreenshots() {
        String screenshotPath = getPara("screenshotPath");
        if (StrKit.isBlank(screenshotPath)) {
            renderJsonFail("截图路径不能为空");
            return;
        }

        try {
            // 获取文件对象
            File file = new File(screenshotPath);
            if (!file.exists()) {
                renderJsonFail("截图文件不存在");
                return;
            }

            // 获取文件所在目录
            File dir = file.getParentFile();
            if (!dir.exists() || !dir.isDirectory()) {
                renderJsonFail("截图目录不存在");
                return;
            }

            // 获取目录下所有图片文件
            File[] files = dir.listFiles((d, name) -> {
                String lowerName = name.toLowerCase();
                return lowerName.endsWith(".jpg") || lowerName.endsWith(".jpeg") ||
                        lowerName.endsWith(".png") || lowerName.endsWith(".gif") ||
                        lowerName.endsWith(".bmp") || lowerName.endsWith(".webp");
            });

            if (files == null || files.length == 0) {
                renderJson(Ret.ok("data", new ArrayList<Map<String, Object>>()));
                return;
            }

            // 将文件信息转换为JSON格式
            List<Map<String, Object>> result = new ArrayList<>();
            for (File imgFile : files) {
                Map<String, Object> fileInfo = new HashMap<>();
                fileInfo.put("name", imgFile.getName());
                fileInfo.put("path", imgFile.getAbsolutePath());
                fileInfo.put("size", formatFileSize(imgFile.length()));
                fileInfo.put("lastModified", new Date(imgFile.lastModified()));
                result.add(fileInfo);
            }

            // 按文件名排序
            result.sort((a, b) -> ((String) a.get("name")).compareTo((String) b.get("name")));

            // 返回结果
            renderJson(Ret.ok("data", result));
        } catch (Exception e) {
            LOG.error("获取截图文件失败", e);
            renderJsonFail("获取截图文件失败: " + e.getMessage());
        }
    }

    /**
     * 格式化文件大小
     *
     * @param size 文件大小（字节）
     * @return 格式化后的文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024 * 1024));
        }
    }

    /**
     * 清理邮件内容中可能导致显示问题的元素
     *
     * @param content 原始邮件内容
     * @return 清理后的内容
     */
    private String cleanEmailSignature(String content) {
        if (content == null) {
            return "";
        }

        // 移除带有黑色背景的分隔线元素
        content = content.replaceAll("<td\\s+style=\"[^\"]*background-color:\\s*rgb\\(0,\\s*0,\\s*1\\)[^\"]*\"[^>]*>[^<]*</td>", "");
        content = content.replaceAll("<td\\s+style=\"[^\"]*background-color:\\s*#000001[^\"]*\"[^>]*>[^<]*</td>", "");
        content = content.replaceAll("<td\\s+style=\"[^\"]*background-color:\\s*black[^\"]*\"[^>]*>[^<]*</td>", "");

        // 替换为细线分隔符
        content = content.replaceAll("<td\\s+style=\"[^\"]*background-color:\\s*rgb\\(0,\\s*0,\\s*1\\)[^\"]*\"[^>]*>",
                "<td style=\"width:1px; background-color:#cccccc; padding:0;\">");

        return content;
    }

    /**
     * 获取邮件翻译内容，用于引用翻译
     */
    public void getTranslationContent() {
        Long emailId = getLong("emailId");
        if (emailId == null) {
            renderJson(Ret.fail("emailId不能为空"));
            return;
        }

        // 查询邮件翻译内容
        EmailTranslation translation = new EmailTranslation().dao().findFirst("SELECT * FROM email_translation WHERE email_id=?", emailId);

        EmailMessages email = emailsService.findById(emailId);
        if (email == null) {
            renderJson(Ret.fail("未找到邮件"));
            return;
        }

        String translatedSubject = null;
        String translatedContent = null;

        if (translation != null) {
            // 优先使用分离的翻译标题
            translatedSubject = translation.getSubjectTranslated();
            translatedContent = translation.getTranslatedContent();
        }

        // 如果没有翻译标题，但有原始标题，则调用AI翻译标题
        if ((translatedSubject == null || translatedSubject.trim().isEmpty()) &&
                email.getSubject() != null && !email.getSubject().trim().isEmpty()) {

            try {
                // 调用AI翻译标题
                translatedSubject = translateSubjectOnly(email.getSubject());

                // 如果翻译成功，保存到数据库
                if (translatedSubject != null && !translatedSubject.trim().isEmpty()) {
                    if (translation == null) {
                        // 创建新的翻译记录
                        translation = new EmailTranslation();
                        translation.setEmailId(emailId);
                        translation.setSubjectOriginal(email.getSubject());
                        translation.setSubjectTranslated(translatedSubject);
                        translation.setCreateTime(new Date());
                        translation.updateTimestamp();
                        translation.save();
                    } else {
                        // 更新现有记录
                        translation.setSubjectOriginal(email.getSubject());
                        translation.setSubjectTranslated(translatedSubject);
                        translation.updateTimestamp();
                        translation.update();
                    }
                }
            } catch (Exception e) {
                LogKit.error("翻译标题失败: " + e.getMessage(), e);
                // 翻译失败时使用原标题
                translatedSubject = email.getSubject();
            }
        }

        // 如果仍然没有翻译标题，使用原标题
        if (translatedSubject == null || translatedSubject.trim().isEmpty()) {
            translatedSubject = email.getSubject();
        }

        // 获取截图文件
        List<File> files = listEmailScreenshots(email);
        List<Kv> screenshots = new ArrayList<>();
        for (File file : files) {
            Kv screenshot = Kv.create();
            screenshot.set("name", file.getName());
            screenshot.set("path", file.getAbsolutePath());
            screenshot.set("size", file.length());
            screenshots.add(screenshot);
        }

        // 构建返回数据
        Kv data = Kv.create();
        data.set("content", translatedContent);
        data.set("subject", translatedSubject);  // 直接返回翻译标题
        data.set("originalSubject", email.getSubject());  // 也返回原标题供参考
        data.set("screenshots", screenshots);

        renderJson(Ret.ok("msg", "获取翻译内容成功").set("data", data));
    }

    /**
     * 单独翻译标题的方法
     */
    private String translateSubjectOnly(String subject) {
        if (subject == null || subject.trim().isEmpty()) {
            return "";
        }

        try {
            // 获取AI提示词
            AiPrompt aiPrompt = new AiPrompt().dao().findFirst(
                    "select * from ai_prompt where enable='1' and `key`='email_monument_translate' order by id");

            if (aiPrompt == null) {
                // 如果没有找到提示词，使用默认提示词
                String prompt = "请翻译以下邮件标题：" + subject;
                return LlmService.me().callLlmWithImages("Kimi Moonshot", "moonshot-v1-128k-vision-preview",
                        prompt, new HashSet<>());
            } else {
                String prompt = aiPrompt.getSystemContent() + "\n请翻译以下邮件标题：" + subject;
                return LlmService.me().callLlmWithImages("Kimi Moonshot", "moonshot-v1-128k-vision-preview",
                        prompt, new HashSet<>());
            }
        } catch (Exception e) {
            LogKit.error("调用AI翻译标题失败: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 批量标记邮件已读
     */
    public void batchMarkRead() {
        String idsParam = getPara("ids");
        if (StrKit.isBlank(idsParam)) {
            renderJsonFail("邮件ID列表不能为空");
            return;
        }

        try {
            // 解析邮件ID列表，支持逗号分隔的字符串
            String[] idArray = idsParam.split(",");
            List<String> ids = new ArrayList<>();
            for (String id : idArray) {
                if (StrKit.notBlank(id.trim())) {
                    ids.add(id.trim());
                }
            }

            if (ids.isEmpty()) {
                renderJsonFail("邮件ID列表不能为空");
                return;
            }

            Ret result = service.batchMarkRead(ids);
            renderJson(result);
        } catch (Exception e) {
            renderJsonFail("批量标记已读失败：" + e.getMessage());
        }
    }

    /**
     * 批量标记邮件未读
     */
    public void batchMarkUnread() {
        String idsParam = getPara("ids");
        if (StrKit.isBlank(idsParam)) {
            renderJsonFail("邮件ID列表不能为空");
            return;
        }

        try {
            // 解析邮件ID列表，支持逗号分隔的字符串
            String[] idArray = idsParam.split(",");
            List<String> ids = new ArrayList<>();
            for (String id : idArray) {
                if (StrKit.notBlank(id.trim())) {
                    ids.add(id.trim());
                }
            }

            if (ids.isEmpty()) {
                renderJsonFail("邮件ID列表不能为空");
                return;
            }

            Ret result = service.batchMarkUnread(ids);
            renderJson(result);
        } catch (Exception e) {
            renderJsonFail("批量标记未读失败：" + e.getMessage());
        }
    }

    /**
     * 批量处理邮件
     */
    public void batchFollowUp() {
        String idsParam = getPara("ids");
        if (StrKit.isBlank(idsParam)) {
            renderJsonFail("邮件ID列表不能为空");
            return;
        }

        try {
            // 解析邮件ID列表，支持逗号分隔的字符串
            String[] idArray = idsParam.split(",");
            List<String> ids = new ArrayList<>();
            for (String id : idArray) {
                if (StrKit.notBlank(id.trim())) {
                    ids.add(id.trim());
                }
            }

            if (ids.isEmpty()) {
                renderJsonFail("邮件ID列表不能为空");
                return;
            }

            Ret result = service.batchFollowUp(ids);
            renderJson(result);
        } catch (Exception e) {
            renderJsonFail("批量处理失败：" + e.getMessage());
        }
    }

    /**
     * 批量标记邮件重要
     */
    public void batchMarkImportant() {
        String idsParam = getPara("ids");
        if (StrKit.isBlank(idsParam)) {
            renderJsonFail("邮件ID列表不能为空");
            return;
        }

        try {
            // 解析邮件ID列表，支持逗号分隔的字符串
            String[] idArray = idsParam.split(",");
            List<String> ids = new ArrayList<>();
            for (String id : idArray) {
                if (StrKit.notBlank(id.trim())) {
                    ids.add(id.trim());
                }
            }

            if (ids.isEmpty()) {
                renderJsonFail("邮件ID列表不能为空");
                return;
            }

            Ret result = service.batchMarkImportant(ids);
            renderJson(result);
        } catch (Exception e) {
            renderJsonFail("批量标记重要失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除邮件
     */
    public void batchDelete() {
        String idsParam = getPara("ids");
        if (StrKit.isBlank(idsParam)) {
            renderJsonFail("邮件ID列表不能为空");
            return;
        }

        try {
            // 解析邮件ID列表，支持逗号分隔的字符串
            String[] idArray = idsParam.split(",");
            List<String> ids = new ArrayList<>();
            for (String id : idArray) {
                if (StrKit.notBlank(id.trim())) {
                    ids.add(id.trim());
                }
            }

            if (ids.isEmpty()) {
                renderJsonFail("邮件ID列表不能为空");
                return;
            }

            Ret result = service.batchDelete(ids);
            renderJson(result);
        } catch (Exception e) {
            renderJsonFail("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 修复邮件附件状态
     */
    @CheckPermission(PermissionKey.EMAIL_MESSAGES_ALL)
    public void fixAttachmentStatus() {
        try {
            String emailIdStr = getPara("emailId");

            if (StrKit.notBlank(emailIdStr)) {
                // 修复指定邮件
                Long emailId = Long.parseLong(emailIdStr);
                boolean success = emailAttachmentFixService.fixEmailAttachmentStatus(emailId);
                if (success) {
                    renderJsonSuccess("邮件附件状态修复成功");
                } else {
                    renderJsonFail("邮件附件状态修复失败");
                }
            } else {
                // 修复所有邮件
                int fixedCount = emailAttachmentFixService.fixAllEmailAttachmentStatus();
                renderJsonSuccess("邮件附件状态修复完成，共修复 " + fixedCount + " 封邮件");
            }
        } catch (Exception e) {
            LogKit.error("修复邮件附件状态失败", e);
            renderJsonFail("修复失败：" + e.getMessage());
        }
    }

    /**
     * 检查邮件附件状态一致性
     */
    @CheckPermission(PermissionKey.EMAIL_MESSAGES_ALL)
    public void checkAttachmentStatusConsistency() {
        try {
            int inconsistentCount = emailAttachmentFixService.checkAttachmentStatusConsistency();
            renderJsonSuccess("检查完成，发现 " + inconsistentCount + " 封邮件的附件状态不一致");
        } catch (Exception e) {
            LogKit.error("检查邮件附件状态一致性失败", e);
            renderJsonFail("检查失败：" + e.getMessage());
        }
    }

    /**
     * 设置邮件为已处理状态
     */
    @CheckPermission(PermissionKey.EMAIL_MESSAGES_ALL)
    public void setProcessed() {
        String emailId = getPara("emailId");
        if (StrKit.isBlank(emailId)) {
            renderJsonFail("邮件ID不能为空");
            return;
        }

        try {
            EmailMessages email = service.findById(emailId);
            if (email == null) {
                renderJsonFail("邮件不存在");
                return;
            }

            // 设置为已处理状态
            email.setIsFollowUp(1);
            email.setFollowType(1); // 1表示已处理
            email.setFollowUpId(JBoltUserKit.getUserId());
            email.setFollowUpTime(new Date());

            boolean success = email.update();
            if (success) {
                renderJsonSuccess("邮件已标记为处理");
            } else {
                renderJsonFail("标记失败");
            }
        } catch (Exception e) {
            LogKit.error("设置邮件处理状态失败", e);
            renderJsonFail("操作失败：" + e.getMessage());
        }
    }

    /**
     * 设置邮件为未处理状态
     */
    @CheckPermission(PermissionKey.EMAIL_MESSAGES_ALL)
    public void setUnprocessed() {
        String emailId = getPara("emailId");
        if (StrKit.isBlank(emailId)) {
            renderJsonFail("邮件ID不能为空");
            return;
        }

        try {
            EmailMessages email = service.findById(emailId);
            if (email == null) {
                renderJsonFail("邮件不存在");
                return;
            }

            // 设置为未处理状态
            email.setIsFollowUp(0);
            email.setFollowUpTime(null);
            email.setFollowUpId(null);
            email.setFollowType(null);

            boolean success = email.update();
            if (success) {
                renderJsonSuccess("邮件已设为未处理");
            } else {
                renderJsonFail("设置失败");
            }
        } catch (Exception e) {
            LogKit.error("设置邮件未处理状态失败", e);
            renderJsonFail("操作失败：" + e.getMessage());
        }
    }
}