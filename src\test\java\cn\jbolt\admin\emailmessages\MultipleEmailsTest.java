package cn.jbolt.admin.emailmessages;

import cn.jbolt.core.kit.JBoltUserKit;
import cn.jbolt.mail.gpt.InitEnv;
import com.jfinal.kit.Kv;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;

/**
 * 多邮箱地址支持测试
 * 测试getEmailHistory方法支持多个邮箱地址（逗号或分号分隔）
 */
public class MultipleEmailsTest {
    
    private static EmailMessagesService emailMessagesService;
    private static final Long TEST_USER_ID = 1L;
    
    // 测试邮箱地址
    private static final String EMAIL1 = "<EMAIL>";
    private static final String EMAIL2 = "<EMAIL>";
    private static final String EMAIL3 = "<EMAIL>";
    
    public static void main(String[] args) {
        try {
            // 初始化环境
            InitEnv.initEnvironment();
            emailMessagesService = new EmailMessagesService();
            
            // 设置测试用户
            JBoltUserKit.setUserId(TEST_USER_ID);
            
            System.out.println("=== 多邮箱地址支持测试 ===");
            
            // 测试单个邮箱
            testSingleEmail();
            
            // 测试逗号分隔的多个邮箱
            testCommaSeperatedEmails();
            
            // 测试分号分隔的多个邮箱
            testSemicolonSeperatedEmails();
            
            // 测试混合分隔符
            testMixedSeperators();
            
            // 测试包含显示名称的邮箱
            testEmailsWithDisplayNames();
            
            // 测试空格和格式问题
            testEmailsWithSpaces();
            
            // 测试无效邮箱格式
            testInvalidEmailFormats();
            
            System.out.println("=== 测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("测试执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static void testSingleEmail() {
        System.out.println("\n1. 测试单个邮箱地址...");
        Kv params = Kv.by("email", EMAIL1);
        Ret result = emailMessagesService.getEmailHistory(1, 10, params);
        
        if (result.isOk()) {
            System.out.println("✓ 单个邮箱地址测试成功");
        } else {
            System.out.println("✗ 单个邮箱地址测试失败: " + result.getStr("msg"));
        }
    }
    
    public static void testCommaSeperatedEmails() {
        System.out.println("\n2. 测试逗号分隔的多个邮箱...");
        String emails = EMAIL1 + "," + EMAIL2 + "," + EMAIL3;
        Kv params = Kv.by("email", emails);
        Ret result = emailMessagesService.getEmailHistory(1, 10, params);
        
        if (result.isOk()) {
            System.out.println("✓ 逗号分隔多邮箱测试成功");
            System.out.println("  输入: " + emails);
        } else {
            System.out.println("✗ 逗号分隔多邮箱测试失败: " + result.getStr("msg"));
        }
    }
    
    public static void testSemicolonSeperatedEmails() {
        System.out.println("\n3. 测试分号分隔的多个邮箱...");
        String emails = EMAIL1 + ";" + EMAIL2 + ";" + EMAIL3;
        Kv params = Kv.by("email", emails);
        Ret result = emailMessagesService.getEmailHistory(1, 10, params);
        
        if (result.isOk()) {
            System.out.println("✓ 分号分隔多邮箱测试成功");
            System.out.println("  输入: " + emails);
        } else {
            System.out.println("✗ 分号分隔多邮箱测试失败: " + result.getStr("msg"));
        }
    }
    
    public static void testMixedSeperators() {
        System.out.println("\n4. 测试混合分隔符...");
        String emails = EMAIL1 + "," + EMAIL2 + ";" + EMAIL3;
        Kv params = Kv.by("email", emails);
        Ret result = emailMessagesService.getEmailHistory(1, 10, params);
        
        if (result.isOk()) {
            System.out.println("✓ 混合分隔符测试成功");
            System.out.println("  输入: " + emails);
        } else {
            System.out.println("✗ 混合分隔符测试失败: " + result.getStr("msg"));
        }
    }
    
    public static void testEmailsWithDisplayNames() {
        System.out.println("\n5. 测试包含显示名称的邮箱...");
        String emails = "张三 <" + EMAIL1 + ">,李四 <" + EMAIL2 + ">";
        Kv params = Kv.by("email", emails);
        Ret result = emailMessagesService.getEmailHistory(1, 10, params);
        
        if (result.isOk()) {
            System.out.println("✓ 显示名称邮箱测试成功");
            System.out.println("  输入: " + emails);
        } else {
            System.out.println("✗ 显示名称邮箱测试失败: " + result.getStr("msg"));
        }
    }
    
    public static void testEmailsWithSpaces() {
        System.out.println("\n6. 测试包含空格的邮箱...");
        String emails = " " + EMAIL1 + " , " + EMAIL2 + " ; " + EMAIL3 + " ";
        Kv params = Kv.by("email", emails);
        Ret result = emailMessagesService.getEmailHistory(1, 10, params);
        
        if (result.isOk()) {
            System.out.println("✓ 空格处理测试成功");
            System.out.println("  输入: '" + emails + "'");
        } else {
            System.out.println("✗ 空格处理测试失败: " + result.getStr("msg"));
        }
    }
    
    public static void testInvalidEmailFormats() {
        System.out.println("\n7. 测试无效邮箱格式...");
        
        // 测试空字符串
        Kv params1 = Kv.by("email", "");
        Ret result1 = emailMessagesService.getEmailHistory(1, 10, params1);
        if (!result1.isOk() && "邮箱地址不能为空".equals(result1.getStr("msg"))) {
            System.out.println("✓ 空字符串验证正确");
        } else {
            System.out.println("✗ 空字符串验证失败");
        }
        
        // 测试只有分隔符
        Kv params2 = Kv.by("email", ",;,;");
        Ret result2 = emailMessagesService.getEmailHistory(1, 10, params2);
        if (!result2.isOk() && "邮箱地址格式不正确".equals(result2.getStr("msg"))) {
            System.out.println("✓ 无效格式验证正确");
        } else {
            System.out.println("✗ 无效格式验证失败: " + result2.getStr("msg"));
        }
        
        // 测试null值
        Kv params3 = Kv.create();
        Ret result3 = emailMessagesService.getEmailHistory(1, 10, params3);
        if (!result3.isOk() && "邮箱地址不能为空".equals(result3.getStr("msg"))) {
            System.out.println("✓ null值验证正确");
        } else {
            System.out.println("✗ null值验证失败");
        }
    }
}
