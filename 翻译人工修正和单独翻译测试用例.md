# 翻译人工修正和单独翻译功能测试用例

## 测试环境准备

1. 确保邮件翻译页面可以正常访问
2. 准备测试邮件数据（包含标题、内容、图片）
3. 配置AI提示词数据（aiPrompt表）
4. 配置AI提供商和模型数据（llmProvider、llmModel表）
5. 确保相关API接口正常工作

## 功能测试用例

### 1. 单独翻译功能测试

#### 1.1 标题单独翻译测试

**测试步骤**:
1. 打开包含标题的邮件翻译页面
2. 点击标题区域的"翻译"按钮
3. 在弹窗中选择提示词
4. 选择AI提供商和模型
5. 点击"开始翻译"

**预期结果**:
- 弹窗正常显示翻译配置选项
- 提示词列表正确加载
- 提供商和模型级联选择正常
- 翻译完成后标题区域显示翻译结果
- 显示"标题翻译完成"状态

#### 1.2 内容单独翻译测试

**测试步骤**:
1. 打开包含内容的邮件翻译页面
2. 点击内容区域的"翻译"按钮
3. 选择自定义提示词选项
4. 输入自定义提示词
5. 选择AI提供商和模型
6. 点击"开始翻译"

**预期结果**:
- 自定义提示词输入框正确显示/隐藏
- 原始内容预览正确显示
- 翻译完成后内容区域显示翻译结果
- 显示"内容翻译完成"状态

#### 1.3 图片单独翻译测试

**测试步骤**:
1. 打开包含图片的邮件翻译页面
2. 点击图片区域的"翻译图片"按钮
3. 配置翻译参数
4. 执行翻译

**预期结果**:
- 图片翻译配置弹窗正确显示
- 翻译完成后图片区域显示翻译结果
- 多张图片的翻译结果正确显示

### 2. 人工修正功能测试

#### 2.1 标题编辑测试

**测试步骤**:
1. 完成标题翻译
2. 点击标题区域的"编辑"按钮
3. 在编辑弹窗中修改翻译内容
4. 点击"保存"

**预期结果**:
- 编辑弹窗正确显示当前翻译内容
- 修改后的内容正确保存到数据库
- 标题区域显示"标题翻译（已编辑）"状态
- 完整翻译内容同步更新

#### 2.2 内容编辑测试

**测试步骤**:
1. 完成内容翻译
2. 点击内容区域的"编辑"按钮
3. 在编辑弹窗中修改翻译内容
4. 点击"保存"

**预期结果**:
- 编辑弹窗正确显示当前翻译内容
- 支持多行文本编辑
- 修改后的内容正确保存
- 内容区域显示"内容翻译（已编辑）"状态

#### 2.3 图片翻译编辑测试

**测试步骤**:
1. 完成图片翻译
2. 点击图片区域的"编辑"按钮
3. 在编辑弹窗中修改各张图片的翻译内容
4. 点击"保存"

**预期结果**:
- 编辑弹窗显示所有图片的翻译内容
- 支持分别编辑每张图片的翻译
- 修改后的内容正确保存
- 图片翻译结果正确更新

### 3. 数据加载测试

#### 3.1 提示词加载测试

**测试步骤**:
1. 打开单独翻译弹窗
2. 检查提示词下拉列表
3. 选择不同的提示词选项

**预期结果**:
- 只显示启用的提示词（enable=1）
- 提示词按排序显示
- 选择"自定义提示词"时显示输入框
- 提示词数据正确加载

#### 3.2 AI模型级联加载测试

**测试步骤**:
1. 打开单独翻译弹窗
2. 选择不同的AI提供商
3. 观察模型列表的变化

**预期结果**:
- 提供商列表只显示启用的提供商
- 选择提供商后模型列表正确更新
- 模型列表只显示对应提供商的模型
- 级联关系正确工作

### 4. 错误处理测试

#### 4.1 参数验证测试

**测试步骤**:
1. 打开单独翻译弹窗
2. 不选择提示词直接翻译
3. 不选择提供商直接翻译
4. 不选择模型直接翻译

**预期结果**:
- 显示相应的错误提示信息
- 不会发送无效的翻译请求
- 用户可以修正参数后重新尝试

#### 4.2 网络错误处理测试

**测试步骤**:
1. 断开网络连接
2. 尝试进行单独翻译
3. 尝试保存编辑内容

**预期结果**:
- 显示网络错误提示
- 加载状态正确关闭
- 用户可以重新尝试操作

#### 4.3 数据不存在处理测试

**测试步骤**:
1. 尝试编辑不存在的翻译内容
2. 使用不存在的提示词ID
3. 使用不存在的模型ID

**预期结果**:
- 显示相应的错误提示
- 不会导致系统崩溃
- 错误信息清晰明确

### 5. 界面交互测试

#### 5.1 弹窗显示测试

**测试步骤**:
1. 测试各种弹窗的显示和关闭
2. 测试弹窗的大小和位置
3. 测试弹窗的响应式适配

**预期结果**:
- 弹窗正确居中显示
- 弹窗大小适合内容
- 移动端显示正常
- 关闭按钮正常工作

#### 5.2 按钮状态测试

**测试步骤**:
1. 观察翻译和编辑按钮的状态变化
2. 测试按钮的禁用和启用
3. 测试按钮的视觉反馈

**预期结果**:
- 按钮状态与功能状态同步
- 禁用状态下按钮不可点击
- 按钮图标和文字正确显示

### 6. 数据一致性测试

#### 6.1 翻译结果同步测试

**测试步骤**:
1. 进行单独翻译
2. 检查各个区域的翻译结果
3. 检查完整翻译内容

**预期结果**:
- 单独翻译结果正确显示在对应区域
- 完整翻译内容包含所有翻译部分
- 数据库记录正确更新

#### 6.2 编辑后同步测试

**测试步骤**:
1. 编辑翻译内容
2. 检查各个区域的显示
3. 检查完整翻译内容的更新

**预期结果**:
- 编辑后的内容正确显示
- 完整翻译内容同步更新
- 编辑状态正确标识

### 7. 性能测试

#### 7.1 大内容翻译测试

**测试步骤**:
1. 使用包含大量文本的邮件
2. 进行单独翻译
3. 观察翻译速度和系统响应

**预期结果**:
- 大内容翻译正常完成
- 系统保持响应
- 内存使用合理

#### 7.2 多图片翻译测试

**测试步骤**:
1. 使用包含多张图片的邮件
2. 进行图片翻译
3. 观察翻译过程和结果

**预期结果**:
- 多张图片翻译正常完成
- 翻译结果正确对应
- 系统性能稳定

### 8. 兼容性测试

#### 8.1 浏览器兼容性测试

**测试浏览器**:
- Chrome (最新版本)
- Firefox (最新版本)
- Safari (最新版本)
- Edge (最新版本)

**预期结果**:
- 所有功能在各浏览器中正常工作
- 弹窗显示一致
- 交互体验流畅

#### 8.2 移动端适配测试

**测试步骤**:
1. 在移动设备上打开翻译页面
2. 测试所有新增功能
3. 观察响应式布局

**预期结果**:
- 按钮大小适合触摸操作
- 弹窗在小屏幕上正常显示
- 功能完全可用

## 测试数据准备

### 1. 提示词数据
```sql
INSERT INTO ai_prompt (key, remark, system_content, user_content, enable) VALUES
('translate_title', '标题翻译', '你是一个专业的翻译助手', '请翻译以下标题内容', 1),
('translate_content', '内容翻译', '你是一个专业的翻译助手', '请翻译以下邮件内容', 1),
('translate_image', '图片翻译', '你是一个专业的翻译助手', '请翻译图片中的文字内容', 1);
```

### 2. AI提供商数据
```sql
INSERT INTO llm_provider (name, api_type, api_base_url, status) VALUES
('测试提供商', 'openai', 'https://api.test.com', 1);
```

### 3. AI模型数据
```sql
INSERT INTO llm_model (provider_id, model_name, model_identifier, status) VALUES
(1, '测试模型', 'test-model', 1);
```

## 测试报告模板

### 测试结果记录

| 测试用例 | 测试结果 | 问题描述 | 严重程度 | 状态 |
|---------|---------|---------|---------|------|
| 标题单独翻译 | ✅/❌ | | 高/中/低 | 通过/失败 |
| 内容单独翻译 | ✅/❌ | | 高/中/低 | 通过/失败 |
| 图片单独翻译 | ✅/❌ | | 高/中/低 | 通过/失败 |
| 标题编辑 | ✅/❌ | | 高/中/低 | 通过/失败 |
| 内容编辑 | ✅/❌ | | 高/中/低 | 通过/失败 |
| 图片编辑 | ✅/❌ | | 高/中/低 | 通过/失败 |
| 数据加载 | ✅/❌ | | 高/中/低 | 通过/失败 |
| 错误处理 | ✅/❌ | | 高/中/低 | 通过/失败 |

### 测试总结

- **测试通过率**: ___%
- **主要问题**: 
- **性能表现**: 
- **用户体验**: 
- **发布建议**: 建议发布/需要修复后发布/不建议发布
