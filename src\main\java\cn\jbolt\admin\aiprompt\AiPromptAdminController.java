package cn.jbolt.admin.aiprompt;

import com.jfinal.aop.Inject;
import cn.jbolt.core.controller.base.JBoltBaseController;
import cn.jbolt.core.permission.CheckPermission;
import cn.jbolt._admin.permission.PermissionKey;
import cn.jbolt.core.permission.UnCheckIfSystemAdmin;
import com.jfinal.core.Path;
import com.jfinal.aop.Before;
import cn.jbolt.core.permission.JBoltAdminAuthInterceptor;
import com.jfinal.core.paragetter.Para;
import com.jfinal.plugin.activerecord.tx.Tx;
import com.jfinal.plugin.activerecord.tx.TxConfig;
import cn.jbolt.core.base.JBoltMsg;
import cn.jbolt.common.model.AiPrompt;
import cn.jbolt.core.permission.UnCheck;
/**
 * 提示词表
 * @ClassName: AiPromptAdminController
 * @author: 总管理
 * @date: 2025-03-24 14:27
 */
@CheckPermission(PermissionKey.ADMIN_AIPROMPT)
@UnCheckIfSystemAdmin
@Before(JBoltAdminAuthInterceptor.class)
@Path(value = "/admin/aiPrompt", viewPath = "/_view/admin/aiprompt")
public class AiPromptAdminController extends JBoltBaseController {

	@Inject
	private AiPromptService service;
   /**
	* 首页
	*/
	public void index() {
		render("index.html");
	}
   /**
	* 数据源
	*/
	public void datas() {
		renderJsonData(service.getAdminDatas(getPageNumber(), getPageSize(), getKeywords(), getSortColumn("sort_rank"), getSortType("asc"), getBoolean("enable")));
	}
	
   /**
	* 新增
	*/
	public void add() {
		render("add.html");
	}
	
   /**
	* 保存
	*/
	@Before(Tx.class)
	public void save(@Para("aiPrompt")AiPrompt aiPrompt) {
		renderJson(service.save(aiPrompt));
	}
	
   /**
	* 编辑
	*/
	public void edit() {
		AiPrompt aiPrompt=service.findById(getInt(0));
		if(aiPrompt == null){
			renderFail(JBoltMsg.DATA_NOT_EXIST);
			return;
		}
		set("aiPrompt",aiPrompt);
		render("edit.html");
	}
	
   /**
	* 更新
	*/
	@Before(Tx.class)
	public void update(@Para("aiPrompt")AiPrompt aiPrompt) {
		renderJson(service.update(aiPrompt));
	}
	
   /**
	* 删除
	*/
	@Before(Tx.class)
	public void delete() {
		renderJson(service.deleteById(getInt(0)));
	}
	
   /**
	* 排序 上移
	*/
    @Before(Tx.class)
    public void up() {
		renderJson(service.up(getInt(0)));
	}
	
   /**
	* 排序 下移
	*/
	@Before(Tx.class)
	public void down() {
		renderJson(service.down(getInt(0)));
	}
	
   /**
	* 排序 初始化
	*/
	@Before(Tx.class)
    public void initSortRank() {
		renderJson(service.initSortRank());
	}
	
   /**
	* 切换启用状态
	*/
	@Before(Tx.class)
	public void toggleEnable() {
		renderJson(service.toggleEnable(getInt(0)));
	}
	
   /**
	* autocomplete组件专用数据
	*/
	@UnCheck
	public void autocompleteDatas() {
        renderJsonData(service.getAutocompleteList(get("q"), getInt("limit",20),true,"key,system_content,user_content"));

	}
	
   /**
	* 得到select radio checkbox专用options数据
	*/
	@UnCheck
	public void options() {
       renderJsonData(service.getOptionListEnable("name","id"));
	}

   /**
	* 获取启用的提示词列表
	*/
	@UnCheck
	public void list() {
		renderJsonData(service.getEnabledList());
	}

}
