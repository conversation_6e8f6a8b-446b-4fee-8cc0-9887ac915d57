<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>翻译内容插入修复测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
</head>
<body>
    <div class="container mt-4">
        <h2>翻译内容插入修复测试</h2>
        
        <!-- 模拟原始翻译内容 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>原始翻译内容（模拟从翻译界面获取的内容）</h5>
            </div>
            <div class="card-body">
                <div id="originalTranslationContent">
                    <h3>询价</h3>
                    <div style="background-color: #f9f9f9; padding: 10px; margin: 10px 0;">
                        <p><strong>30x20 双心 Orion 1x, Himalaya 2x, Shanxi Black 3x</strong></p>
                        <p>Trittplatte: 30x30x5 Orion 8x, Aurora 4x, Himalaya 4x</p>
                        <p>Trittplatte: 35x35x5 Orion 6x, Aurora 6x, Himalaya 5x, Shanxi Black 6x</p>
                        <p>Fau01 40x30x12 Orion 2x, Aurora 3x, Himalaya 4x</p>
                        <p>Einfassung 6/15 125 cm Himalaya 2x, Shanxi Black 2x</p>
                        <p>Einfassung 6/15 150x138 cm Set Shanxi Black 3x</p>
                        <p>Einfassung 6/15 80 cm Orion 3x, Viskont White 1x, Shanxi Black 6x</p>
                        <p>Einfassung 6/15 128 cm Orion 5x, Aurora 2x, Viskont White 4x, Shanxi Black 6x</p>
                        <p>Einfassung 6/15 250 cm Orion 12x, Himalaya 8x, Shanxi Black 10x</p>
                        <p>Einfassung 6/15 263 cm Orion 2x, Aurora 10x, Himalaya 8x, Viskont White 2x, Shanxi Black 12x</p>
                    </div>
                    <div style="background-color: #f0f8ff; padding: 10px; margin: 10px 0;">
                        <p><strong>室外外围 6/15 125厘米 白马拉雅花岗岩 2块，山西黑花岗岩 2块</strong></p>
                        <p>室外外围 6/15 150x138厘米套装 山西黑花岗岩 3块</p>
                        <p>室外外围 6/15 80厘米 白马拉雅花岗岩 3块，威斯康特白花岗岩 1块，山西黑花岗岩 6块</p>
                        <p>室外外围 6/15 128厘米 白马拉雅花岗岩 5块，奥罗拉花岗岩 2块，威斯康特白花岗岩 4块，山西黑花岗岩 6块</p>
                        <p>室外外围 6/15 263厘米 白马拉雅花岗岩 2块，奥罗拉花岗岩 10块，喜马拉雅花岗岩 8块，威斯康特白花岗岩 2块，山西黑花岗岩 12块</p>
                        <p>室外外围 6/15 300厘米 喜马拉雅花岗岩 4块，山西黑花岗岩 4块</p>
                        <p>室外外围 5/15 80厘米 喜马拉雅花岗岩 2块，威斯康特白花岗岩 2块，山西黑花岗岩 4块</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试区域 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>测试区域</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary" onclick="testFormatTranslationContent()">
                    <i class="fa fa-test-tube"></i> 测试格式化函数
                </button>
                <button class="btn btn-success ms-2" onclick="testInsertContent()">
                    <i class="fa fa-plus"></i> 测试插入内容
                </button>
                <button class="btn btn-info ms-2" onclick="clearEditor()">
                    <i class="fa fa-eraser"></i> 清空编辑器
                </button>
            </div>
        </div>

        <!-- 格式化结果显示 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>格式化后的内容</h5>
            </div>
            <div class="card-body">
                <div id="formattedContent" style="border: 1px solid #ddd; padding: 10px; min-height: 100px; background-color: #f8f9fa;">
                    点击"测试格式化函数"查看结果
                </div>
            </div>
        </div>

        <!-- TinyMCE 编辑器 -->
        <div class="card">
            <div class="card-header">
                <h5>TinyMCE 编辑器（模拟回复邮件编辑器）</h5>
            </div>
            <div class="card-body">
                <textarea id="testEditor"></textarea>
            </div>
        </div>
    </div>

    <script>
        // 初始化 TinyMCE 编辑器
        tinymce.init({
            selector: '#testEditor',
            height: 400,
            plugins: 'advlist autolink lists link image charmap print preview anchor searchreplace visualblocks code fullscreen insertdatetime media table paste code help wordcount',
            toolbar: 'undo redo | formatselect | bold italic backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
            content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; }'
        });

        // 修复后的格式化翻译内容函数
        function formatTranslationContent(content) {
            if (!content) return '';
            
            // 检查是否已经是HTML格式（包含HTML标签）
            const htmlTagRegex = /<[a-z][\s\S]*>/i;
            if (htmlTagRegex.test(content)) {
                // 如果已经是HTML，直接返回，保持原有格式
                return content.trim();
            }
            
            // 对于纯文本内容，进行简单的HTML转换
            let formattedContent = content
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;');
            
            // 统一换行符处理
            formattedContent = formattedContent
                .replace(/\r\n/g, '\n')  // Windows换行符转为Unix
                .replace(/\r/g, '\n');   // Mac换行符转为Unix
            
            // 将换行符转换为HTML换行
            formattedContent = formattedContent.replace(/\n/g, '<br>');
            
            // 处理制表符
            formattedContent = formattedContent.replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;');
            
            // 处理多个连续空格
            formattedContent = formattedContent.replace(/ {2,}/g, function(match) {
                return '&nbsp;'.repeat(match.length);
            });
            
            // 包装在段落标签中
            return '<p>' + formattedContent + '</p>';
        }

        // 测试格式化函数
        function testFormatTranslationContent() {
            const originalContent = $('#originalTranslationContent').html();
            const formattedContent = formatTranslationContent(originalContent);
            $('#formattedContent').html(formattedContent);
        }

        // 测试插入内容到编辑器
        function testInsertContent() {
            const translationContent = $('#formattedContent').html();
            
            if (!translationContent || translationContent.includes('点击"测试格式化函数"查看结果')) {
                alert('请先点击"测试格式化函数"');
                return;
            }
            
            // 获取编辑器实例
            const editor = tinymce.get('testEditor');
            if (!editor) {
                alert('编辑器未初始化');
                return;
            }
            
            // 清理翻译内容，移除可能的外层容器
            let cleanedContent = translationContent;
            
            // 如果内容被包装在alert div中，提取实际内容
            const alertMatch = cleanedContent.match(/<div class="alert[^"]*"[^>]*>(.*?)<\/div>/s);
            if (alertMatch) {
                cleanedContent = alertMatch[1];
            }
            
            // 构建要插入的内容，使用更简洁的样式
            const insertContent = `
                <div style="margin: 15px 0; padding: 15px; border-left: 4px solid #007bff; background-color: #f8f9fa; border-radius: 4px;">
                    <div style="color: #007bff; font-weight: bold; margin-bottom: 10px; font-size: 14px;">
                        <i class="fa fa-language"></i> 翻译内容
                    </div>
                    <div style="line-height: 1.6; color: #333;">
                        ${cleanedContent}
                    </div>
                </div>
                <p><br></p>
            `;
            
            // 在光标位置插入翻译内容
            editor.execCommand('mceInsertContent', false, insertContent);
            
            alert('翻译内容已插入到编辑器');
        }

        // 清空编辑器
        function clearEditor() {
            const editor = tinymce.get('testEditor');
            if (editor) {
                editor.setContent('');
            }
        }
    </script>
</body>
</html>
