package cn.jbolt.admin.aiprompt;

import com.jfinal.plugin.activerecord.Page;
import java.util.List;
import cn.jbolt.extend.systemlog.ProjectSystemLogTargetType;
import cn.jbolt.core.service.base.JBoltBaseService;
import com.jfinal.kit.Kv;
import com.jfinal.kit.Okv;
import com.jfinal.kit.Ret;
import cn.jbolt.core.base.JBoltMsg;
import cn.jbolt.core.db.sql.Sql;
import cn.jbolt.common.model.AiPrompt;
/**
 * 提示词表
 * @ClassName: AiPromptService
 * @author: 总管理
 * @date: 2025-03-24 14:27  
 */
public class AiPromptService extends JBoltBaseService<AiPrompt> {
	private final AiPrompt dao=new AiPrompt().dao();
	@Override
	protected AiPrompt dao() {
		return dao;
	}

	@Override
    protected int systemLogTargetType() {
        return ProjectSystemLogTargetType.NONE.getValue();
    }
		
	/**
	 * 后台管理数据查询
	 * @param pageNumber 第几页
	 * @param pageSize   每页几条数据
	 * @param keywords   关键词
	 * @param sortColumn  排序列名
	 * @param sortType  排序方式 asc desc
     * @param enable 是否可用
	 * @return
	 */
	public Page<AiPrompt> getAdminDatas(int pageNumber, int pageSize, String keywords, String sortColumn, String sortType, Boolean enable) {
	    //创建sql对象
	    Sql sql = selectSql().page(pageNumber,pageSize);
	    //sql条件处理
        sql.eqBooleanToChar("enable",enable);
        //关键词模糊查询
        sql.like("remark",keywords);
        //排序
        sql.orderBy(sortColumn,sortType);
		return paginate(sql);
	}
	
	/**
	 * 保存
	 * @param aiPrompt
	 * @return
	 */
	public Ret save(AiPrompt aiPrompt) {
		if(aiPrompt==null || isOk(aiPrompt.getId())) {
			return fail(JBoltMsg.PARAM_ERROR);
		}
		aiPrompt.setSortRank(getNextSortRank());
		boolean success=aiPrompt.save();
		if(success) {
			//添加日志
			//addSaveSystemLog(aiPrompt.getId(), JBoltUserKit.getUserId(), aiPrompt.getName());
		}
		return ret(success);
	}
	
	/**
	 * 更新
	 * @param aiPrompt
	 * @return
	 */
	public Ret update(AiPrompt aiPrompt) {
		if(aiPrompt==null || notOk(aiPrompt.getId())) {
			return fail(JBoltMsg.PARAM_ERROR);
		}
		//更新时需要判断数据存在
		AiPrompt dbAiPrompt=findById(aiPrompt.getId());
		if(dbAiPrompt==null) {return fail(JBoltMsg.DATA_NOT_EXIST);}
		boolean success=aiPrompt.update();
		if(success) {
			//添加日志
			//addUpdateSystemLog(aiPrompt.getId(), JBoltUserKit.getUserId(), aiPrompt.getName());
		}
		return ret(success);
	}
	
	/**
	 * 删除数据后执行的回调
	 * @param aiPrompt 要删除的model
	 * @param kv 携带额外参数一般用不上
	 * @return
	 */
	@Override
	protected String afterDelete(AiPrompt aiPrompt, Kv kv) {
		//addDeleteSystemLog(aiPrompt.getId(), JBoltUserKit.getUserId(),aiPrompt.getName());
		return null;
	}
	
	/**
	 * 检测是否可以删除
	 * @param aiPrompt model
	 * @param kv 携带额外参数一般用不上
	 * @return
	 */
	@Override
	public String checkInUse(AiPrompt aiPrompt, Kv kv) {
		//这里用来覆盖 检测是否被其它表引用
		return null;
	}
	
	/**
	 * 上移
	 * @param id
	 * @return
	 */
	public Ret up(Integer id) {
		AiPrompt aiPrompt=findById(id);
		if(aiPrompt==null){
			return fail("数据不存在或已被删除");
		}
		Integer rank=aiPrompt.getSortRank();
		if(rank==null||rank<=0){
			return fail("顺序需要初始化");
		}
		if(rank==1){
			return fail("已经是第一个");
		}
		AiPrompt upAiPrompt=findFirst(Okv.by("sort_rank", rank-1));
		if(upAiPrompt==null){
			return fail("顺序需要初始化");
		}
		upAiPrompt.setSortRank(rank);
		aiPrompt.setSortRank(rank-1);
		
		upAiPrompt.update();
		aiPrompt.update();
		return SUCCESS;
	}
	
	/**
	 * 下移
	 * @param id
	 * @return
	 */
	public Ret down(Integer id) {
		AiPrompt aiPrompt=findById(id);
		if(aiPrompt==null){
			return fail("数据不存在或已被删除");
		}
		Integer rank=aiPrompt.getSortRank();
		if(rank==null||rank<=0){
			return fail("顺序需要初始化");
		}
		int max=getCount();
		if(rank==max){
			return fail("已经是最后已一个");
		}
		AiPrompt upAiPrompt=findFirst(Okv.by("sort_rank", rank+1));
		if(upAiPrompt==null){
			return fail("顺序需要初始化");
		}
		upAiPrompt.setSortRank(rank);
		aiPrompt.setSortRank(rank+1);
		
		upAiPrompt.update();
		aiPrompt.update();
		return SUCCESS;
	}
	
	/**
	 * 初始化排序
	 */
	public Ret initSortRank(){
		List<AiPrompt> allList=findAll();
		if(allList.size()>0){
			for(int i=0;i<allList.size();i++){
				allList.get(i).setSortRank(i+1);
			}
			batchUpdate(allList);
		}
		//添加日志
		//addUpdateSystemLog(null, JBoltUserKit.getUserId(), "所有数据", "的顺序:初始化所有");
		return SUCCESS;
	}
	
	/**
	 * toggle操作执行后的回调处理
	 */
	@Override
	protected String afterToggleBoolean(AiPrompt aiPrompt, String column, Kv kv) {
		//addUpdateSystemLog(aiPrompt.getId(), JBoltUserKit.getUserId(), aiPrompt.getName(),"的字段["+column+"]值:"+aiPrompt.get(column));
		/**
		switch(column){
		    case "enable":
		        break;
		}
		*/
		return null;
	}

	/**
	 * 获取启用的提示词列表
	 * @return
	 */
	public List<AiPrompt> getEnabledList() {
		return find("SELECT * FROM ai_prompt WHERE enable = '1' ORDER BY sort_rank ASC");
	}

}