# 翻译人工修正和单独翻译功能说明

## 功能概述

为邮件翻译界面新增了两个重要功能：
1. **人工修正翻译内容** - 支持对翻译结果进行手动编辑和修正
2. **单独翻译功能** - 支持对标题、内容、图片分别进行单独翻译，可选择不同的提示词和AI模型

## 主要功能

### 1. 人工修正翻译内容

#### 功能特点
- **编辑按钮**: 每个翻译结果区域都有独立的编辑按钮
- **弹窗编辑**: 使用弹窗形式进行编辑，避免页面混乱
- **实时保存**: 编辑后立即保存到数据库
- **状态标识**: 编辑后的内容会显示"已编辑"标识

#### 支持的编辑类型
- **标题翻译编辑**: 修正翻译后的邮件标题
- **内容翻译编辑**: 修正翻译后的邮件正文
- **图片翻译编辑**: 批量编辑多张图片的翻译内容

### 2. 单独翻译功能

#### 功能特点
- **独立翻译**: 可以单独翻译标题、内容或图片
- **自定义配置**: 每次翻译可选择不同的提示词和AI模型
- **灵活选择**: 支持预设提示词或自定义提示词
- **模型切换**: 支持不同AI提供商和模型的切换

#### 翻译配置选项
- **提示词选择**: 从aiPrompt表中选择预设提示词
- **自定义提示词**: 支持手动输入自定义提示词
- **AI提供商选择**: 从llmProvider表中选择提供商
- **AI模型选择**: 根据提供商动态加载对应的模型列表

## 技术实现

### 前端实现

#### 1. UI组件增强
```html
<!-- 标题翻译区域 -->
<div class="d-flex justify-content-between align-items-center mb-2">
    <h6 class="text-muted mb-0">翻译标题</h6>
    <div class="btn-group">
        <button class="btn btn-sm btn-outline-primary" onclick="translateSingle('subject')">
            <i class="fa fa-language"></i> 翻译
        </button>
        <button class="btn btn-sm btn-outline-secondary" onclick="editTranslation('subject')">
            <i class="fa fa-edit"></i> 编辑
        </button>
    </div>
</div>
```

#### 2. 核心JavaScript函数
- `translateSingle(type)`: 单独翻译功能入口
- `openTranslationDialog()`: 打开翻译配置弹窗
- `loadPromptsAndModels()`: 加载提示词和模型数据
- `executeSingleTranslation()`: 执行单独翻译
- `editTranslation(type)`: 编辑翻译内容
- `saveEditedTranslation()`: 保存编辑的内容

#### 3. 动态数据加载
- 提示词列表动态加载
- AI提供商列表动态加载
- 模型列表根据提供商动态更新

### 后端实现

#### 1. 控制器API扩展
```java
// EmailMessagesAdminController.java
public void translateSingle() {
    // 单独翻译API
}

public void saveEditedTranslation() {
    // 保存编辑的翻译内容API
}
```

#### 2. 翻译插件扩展
```java
// EmailTranslationPlugin.java
public String translateSingle(Long emailId, String type, String originalContent, 
                             Integer promptId, String customPrompt, Long providerId, Long modelId) {
    // 单独翻译实现
}
```

#### 3. 数据访问API
- `AiPromptAdminController.list()`: 获取启用的提示词列表
- `LlmProviderAdminController.list()`: 获取启用的提供商列表
- `LlmModelAdminController.listByProvider()`: 根据提供商获取模型列表

#### 4. LLM服务扩展
```java
// LlmService.java
public String translateWithModel(String text, AiPrompt aiPrompt, Long providerId, Long modelId) {
    // 使用指定模型翻译文本
}

public String translateImageWithModel(String imagePath, AiPrompt aiPrompt, Long providerId, Long modelId) {
    // 使用指定模型翻译图片
}
```

## 数据库交互

### 1. 翻译记录更新
- 单独翻译后更新对应字段（subject_translated, content_translated, images_translation）
- 人工编辑后直接更新数据库记录
- 保持翻译记录的完整性和一致性

### 2. 父子表关系处理
- **llmProvider** (父表) - **llmModel** (子表)
- 动态加载模型列表时根据提供商ID查询
- 确保模型选择的准确性

### 3. 提示词管理
- 从aiPrompt表加载启用的提示词
- 支持系统提示词和用户提示词的组合使用

## 用户体验优化

### 1. 直观的操作界面
- 每个翻译区域都有清晰的操作按钮
- 使用图标和文字结合的按钮设计
- 响应式布局适配不同屏幕尺寸

### 2. 智能的配置选择
- 提示词下拉选择，支持自定义输入
- 提供商和模型的级联选择
- 原始内容预览，方便用户确认

### 3. 友好的反馈机制
- 翻译过程中显示加载状态
- 成功/失败的明确提示
- 编辑状态的视觉标识

## 使用场景

### 1. 翻译质量优化
- AI翻译结果不够准确时进行人工修正
- 专业术语需要特定翻译时的调整
- 语言风格需要统一时的编辑

### 2. 灵活的翻译策略
- 不同内容使用不同的翻译提示词
- 重要内容使用更高质量的AI模型
- 特殊场景使用专门的翻译配置

### 3. 分步翻译处理
- 先翻译标题确认方向
- 再翻译内容进行详细处理
- 最后处理图片内容

## 配置说明

### 1. 提示词配置
- 在aiPrompt表中配置不同用途的提示词
- 设置enable字段为1启用提示词
- 可配置系统提示词和用户提示词

### 2. AI模型配置
- 在llmProvider表中配置AI提供商
- 在llmModel表中配置具体模型
- 确保provider_id正确关联

### 3. 权限配置
- 单独翻译和编辑功能使用@UnCheck注解
- 可根据需要调整权限控制

## 注意事项

### 1. 数据一致性
- 编辑后需要更新完整翻译内容
- 确保各个翻译部分的同步
- 保持翻译记录的时间戳更新

### 2. 性能考虑
- 单独翻译可能增加API调用次数
- 建议合理选择AI模型以平衡质量和速度
- 大量编辑操作时注意数据库性能

### 3. 用户体验
- 提供清晰的操作指引
- 避免误操作导致的数据丢失
- 保持界面的响应性

## 未来扩展

### 1. 批量操作
- 支持批量编辑多个翻译结果
- 批量应用相同的翻译配置
- 批量导入/导出翻译内容

### 2. 翻译历史
- 记录翻译和编辑历史
- 支持版本对比和回滚
- 翻译质量评估和统计

### 3. 智能推荐
- 根据内容类型推荐合适的提示词
- 根据历史使用推荐AI模型
- 智能检测翻译质量并建议优化
