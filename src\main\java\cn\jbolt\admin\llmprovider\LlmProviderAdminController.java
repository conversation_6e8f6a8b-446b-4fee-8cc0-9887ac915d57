package cn.jbolt.admin.llmprovider;

import com.jfinal.aop.Inject;
import cn.jbolt.core.controller.base.JBoltBaseController;
import cn.jbolt.core.permission.CheckPermission;
import cn.jbolt._admin.permission.PermissionKey;
import cn.jbolt.core.permission.UnCheckIfSystemAdmin;
import com.jfinal.core.Path;
import com.jfinal.aop.Before;
import cn.jbolt.core.permission.JBoltAdminAuthInterceptor;
import com.jfinal.core.paragetter.Para;
import com.jfinal.plugin.activerecord.tx.Tx;
import com.jfinal.plugin.activerecord.tx.TxConfig;
import cn.jbolt.core.base.JBoltMsg;
import cn.jbolt.common.model.LlmProvider;
import cn.jbolt.core.permission.UnCheck;
/**
 * 大模型提供商表
 * @ClassName: LlmProviderAdminController
 * @author: 总管理
 * @date: 2025-05-08 16:52
 */
@CheckPermission(PermissionKey.ADMIN_LLMPROVIDER)
@UnCheckIfSystemAdmin
@Before(JBoltAdminAuthInterceptor.class)
@Path(value = "/admin/llmProvider", viewPath = "/_view/admin/llmprovider")
public class LlmProviderAdminController extends JBoltBaseController {

	@Inject
	private LlmProviderService service;
   /**
	* 首页
	*/
	public void index() {
		render("index.html");
	}
   /**
	* 数据源
	*/
	public void datas() {
		renderJsonData(service.getAdminDatas(getPageNumber(), getPageSize(), getKeywords(), getSortColumn("id"), getSortType("desc"), get("apiType"), getBoolean("status")));
	}
	
   /**
	* 新增
	*/
	public void add() {
		render("add.html");
	}
	
   /**
	* 保存
	*/
	@Before(Tx.class)
	public void save(@Para("llmProvider")LlmProvider llmProvider) {
		renderJson(service.save(llmProvider));
	}
	
   /**
	* 编辑
	*/
	public void edit() {
		LlmProvider llmProvider=service.findById(getLong(0));
		if(llmProvider == null){
			renderFail(JBoltMsg.DATA_NOT_EXIST);
			return;
		}
		set("llmProvider",llmProvider);
		render("edit.html");
	}
	
   /**
	* 更新
	*/
	@Before(Tx.class)
	public void update(@Para("llmProvider")LlmProvider llmProvider) {
		renderJson(service.update(llmProvider));
	}
	
   /**
	* 删除
	*/
	@Before(Tx.class)
	public void delete() {
		renderJson(service.deleteById(getLong(0)));
	}
	
   /**
	* 切换status
	*/
	@Before(Tx.class)
	public void toggleStatus() {
	    renderJson(service.toggleBoolean(getLong(0),"status"));
	}
	
   /**
	* autocomplete组件专用数据
	*/
	@UnCheck
	public void autocompleteDatas() {
        renderJsonData(service.getAutocompleteList(get("q"), getInt("limit",20),true,"name,api_base_url,remark"));

	}
	
   /**
	* 得到select radio checkbox专用options数据
	*/
	@UnCheck
	public void options() {
       renderJsonData(service.getOptionList("name","id"));
	}

   /**
	* 获取启用的提供商列表
	*/
	@UnCheck
	public void list() {
		renderJsonData(service.getEnabledList());
	}


}
